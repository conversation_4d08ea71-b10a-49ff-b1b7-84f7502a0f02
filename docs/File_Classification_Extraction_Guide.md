# AI政务智能中枢平台 - 文件分类和提取开发指南

## 概述

文件分类和提取功能是AI政务智能中枢平台的核心能力之一，通过集成百炼AI等第三方服务，实现智能文件识别、内容提取和字段映射。本指南详细介绍文件分类机制、内容提取功能和相关API的使用方法。

## 1. 文件分类机制

### 1.1 文件分类架构

文件分类采用AI驱动的智能识别机制，主要包括：

**核心组件**：
- `MaterialsClassifyComponentEngine` - 材料分类组件引擎
- `MaterialsClassifyGroupComponentEngine` - 材料分组分类组件引擎
- `MaterialClassificationService` - 材料分类服务
- `MattersClassifyService` - 事项分类服务

**分类流程**：
1. 文件上传到百炼AI平台
2. AI识别文件类型和内容
3. 根据业务规则进行分类
4. 提取关键字段信息
5. 更新业务实例数据

### 1.2 文件类型识别

**支持的文件类型**：
- 图片文件：JPG、PNG、PDF等
- 证件类：身份证、营业执照、户口本等
- 表单类：申请表、登记表、证明材料等

**识别算法**：
```java
public JSONArray fileClassifyBl(FileClassifyDTO dto, Map<String, Object> dataMap, String fileClassifyKey) {
    // 1. 获取百炼AI配置
    String baiLianKey = environment.getProperty("baiLian.apiKey", String.class, "sk-a482d257230a49a08107b0cffa31ac8a");
    String baiLianUploadUrl = environment.getProperty("baiLian.uploadUrl", String.class, "https://dashscope.aliyuncs.com/compatible-mode/v1/files");
    
    // 2. 上传文件到百炼平台
    List<FileDTO> bodyList = dto.getFileUrls();
    for (FileDTO body : bodyList) {
       body.setBaiLianId(uploadFileBl(body, baiLianKey, baiLianUploadUrl));
    }
    
    // 3. 构建分类请求数据
    dataMap.put("imageList", bodyList);
    
    // 4. 调用分类服务
    return fileClassifyResult(dto, fileClassifyKey, dataMap);
}
```

### 1.3 材料分类组件

**MaterialsClassifyComponentEngine**：
```java
@Service
public class MaterialsClassifyComponentEngine extends AbstractMaterialClassifyEngine {
    
    @Override
    public ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        
        // 检查是否已有分类结果
        if (chatProcessDTO.getMaterialSubmitVOS() != null && !chatProcessDTO.getMaterialSubmitVOS().isEmpty()) {
            // 返回缓存的分类结果
            ComponentRunVO vo = new ComponentRunVO();
            ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                    .componentName(CODE)
                    .componentInfo(chatProcessDTO.getMaterialSubmitVOS())
                    .build();
            vo.setRenderData(List.of(renderData));
            return vo;
        }
        
        // 执行材料分类处理
        List<BizInstanceMaterialVO> bizInstanceMaterialVOS = bizInstanceMaterialService.getInstanceMaterial(componentDataContext.getInstanceId());
        List<BizInstanceMaterialVO> list = bizInstanceMaterialVOS.stream()
                .filter(bizInstanceMaterialVO -> !bizInstanceMaterialVO.isBackFill())
                .toList();
        return processMaterialClassification(new ArrayList<>(list), chatProcessDTO, componentDataContext);
    }
}
```

## 2. 内容提取功能

### 2.1 AI内容提取架构

**提取流程**：
1. 文件上传到百炼AI平台
2. 指定需要提取的字段类型
3. AI识别并提取字段内容
4. 字段映射和格式化
5. 更新到业务表单

### 2.2 字段提取服务

**核心方法**：
```java
public JSONArray withdrawBl(FileClassifyDTO dto, String fileClassifyKey) {
    Map<String, Object> dataMap = Maps.newHashMap();
    dataMap.put("requestContent", dto.getRecognizeContent());
    
    // 获取百炼AI配置
    String baiLianUploadUrl = environment.getProperty("baiLian.uploadUrl", String.class, "https://dashscope.aliyuncs.com/compatible-mode/v1/files");
    String baiLianKey = environment.getProperty("baiLian.apiKey", String.class, "sk-a482d257230a49a08107b0cffa31ac8a");
    
    // 上传文件到百炼平台
    List<FileDTO> bodyList = dto.getFileUrls();
    for (FileDTO body : bodyList) {
        body.setBaiLianId(uploadFileBl(body, baiLianKey, baiLianUploadUrl));
    }
    dataMap.put("imageList", bodyList);
    
    // 调用字段提取服务
    return fileClassifyResult(dto, fileClassifyKey, dataMap);
}
```

### 2.3 字段映射机制

**字段提取和映射**：
```java
protected Map<String, Object> processMaterialRecognize(List<BizInstanceMaterialVO> instanceMaterialVOList, ComponentDataContext componentDataContext) {
    // 获取表单信息
    BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId());
    
    // 调用材料分类服务进行识别
    MaterialClassificationService.MaterialClassificationResult result = materialClassificationService.processRecognition(
            instanceMaterialVOList,
            bizInstanceFieldsVO,
            fileRecognitionKey);
    
    // 返回提取的字段数据
    return result.getExtractedFields();
}
```

**字段提取逻辑**：
```java
private void extractFields(JSONObject jsonObject, Map<String, String> fieldAliasMap, Map<String, Object> fieldExtractDataMap) {
    // 遍历AI提取的字段
    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
        String fieldName = entry.getKey();
        Object fieldValue = entry.getValue();
        
        // 根据字段别名映射进行转换
        String mappedFieldName = fieldAliasMap.getOrDefault(fieldName, fieldName);
        
        // 更新字段提取数据映射
        if (fieldValue != null && !fieldValue.toString().trim().isEmpty()) {
            fieldExtractDataMap.put(mappedFieldName, fieldValue);
        }
    }
}
```

## 3. API接口详解

### 3.1 文件字段内容提取接口

**接口定义**：
```java
@Operation(summary = "文件字段内容提取-百炼")
@PostMapping(value = "/file/withdrawBl")
ResponseData<Object> withdrawBl(@RequestBody FileClassifyDTO dto);
```

**请求参数 - FileClassifyDTO**：
```java
@Data
public class FileClassifyDTO {
    @Schema(description = "用户id")
    private String userId;              // 必需：用户ID
    
    @Schema(description = "会话id")
    private String chatId;              // 可选：会话ID
    
    @Schema(description = "材料id")
    private List<String> materialIds;   // 可选：材料ID列表
    
    @Schema(description = "文件")
    private List<FileDTO> fileUrls;     // 必需：文件列表
    
    @Schema(description = "需要识别的字段")
    private String recognizeContent;    // 必需：需要提取的字段描述
}
```

**FileDTO结构**：
```java
@Data
public class FileDTO {
    private String id;          // 文件ID
    private String name;        // 文件名称
    private String type;        // 文件类型
    private String url;         // 文件URL
    private String remark;      // 文件备注
    private String baiLianId;   // 百炼平台文件ID
}
```

### 3.2 接口调用示例

**请求示例**：
```json
{
    "userId": "user123",
    "chatId": "chat456",
    "recognizeContent": "姓名,身份证号,出生日期,性别,民族,住址",
    "fileUrls": [
        {
            "id": "file001",
            "name": "身份证.jpg",
            "type": "image/jpeg",
            "url": "https://example.com/files/idcard.jpg",
            "remark": "身份证正面"
        }
    ]
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "姓名": "张三",
        "身份证号": "320123199001011234",
        "出生日期": "1990-01-01",
        "性别": "男",
        "民族": "汉族",
        "住址": "江苏省苏州市工业园区XX路XX号"
    }
}
```

### 3.3 文件上传更新接口

**接口定义**：
```java
@Operation(summary = "更新文件上传列表")
@PostMapping("/file/upload")
ResponseData<Void> updateFileUploadList(@RequestBody UpdateFileUploadDTO updateFileUploadDTO);
```

**使用示例**：
```javascript
// 更新文件上传列表
const updateRequest = {
    recordId: "record_20250801_001",
    materialSubmitVOS: [
        {
            materialId: "material123",
            materialName: "身份证",
            materialFileVOList: [
                {
                    fileId: "file001",
                    fileName: "身份证.jpg",
                    fileUrl: "https://example.com/files/idcard.jpg"
                }
            ]
        }
    ]
};

fetch('/api/chat/file/upload', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(updateRequest)
});
```

## 4. 百炼AI服务集成

### 4.1 配置参数

**环境配置**：
```yaml
# application.yml
baiLian:
  apiKey: sk-a482d257230a49a08107b0cffa31ac8a
  uploadUrl: https://dashscope.aliyuncs.com/compatible-mode/v1/files

# 字段提取Key
FieldExtractionKey: app-bGVVIyj8ZoTiSuLjVnp9wYxJ

# 文件下载URL
file:
  download:
    url: https://servers.workplat.com/zwfw-ai-central/api/configuration/file/download?id=
```

### 4.2 文件上传到百炼平台

**上传实现**：
```java
String uploadFileBl(FileDTO dto, String baiLianKey, String baiLianUploadUrl) {
    // 1. 获取远程文件流
    ResponseEntity<Resource> downloadResponse = restTemplate.exchange(
            dto.getUrl(),
            HttpMethod.GET,
            null,
            Resource.class
    );

    if (downloadResponse.getStatusCode() == HttpStatus.OK && downloadResponse.getBody() != null) {
        try (InputStream inputStream = downloadResponse.getBody().getInputStream()) {
            // 2. 转换为 ByteArrayResource
            byte[] fileBytes = IOUtils.toByteArray(inputStream);
            Resource resource = new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    return dto.getName();
                }
            };

            // 3. 构建 multipart 请求
            MultiValueMap<String, Object> jsonBody = new LinkedMultiValueMap<>();
            jsonBody.add("file", resource);
            jsonBody.add("purpose", "file-extract");

            // 4. 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setBearerAuth(baiLianKey);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(jsonBody, headers);

            // 5. 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    baiLianUploadUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            
            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            if (jsonResponse != null) {
                return jsonResponse.getString("id");
            }
        } catch (Exception e) {
            log.error("文件上传到百炼平台失败", e);
        }
    }
    return null;
}
```

### 4.3 文件删除

**删除实现**：
```java
private void deleteFileBl(String baiLianId) {
    String baiLianKey = environment.getProperty("baiLian.apiKey", String.class, "sk-a482d257230a49a08107b0cffa31ac8a");
    String deleteUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1/files/" + baiLianId;
    
    // 构建请求头
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
    headers.setBearerAuth(baiLianKey);
    HttpEntity<String> requestEntity = new HttpEntity<>(headers);
    
    try {
        ResponseEntity<String> response = restTemplate.exchange(
                deleteUrl,
                HttpMethod.DELETE,
                requestEntity,
                String.class
        );
        
        if (response.getStatusCode() != HttpStatus.OK) {
            log.error("百炼文件删除失败，baiLianId: {}", baiLianId);
        }
    } catch (Exception e) {
        log.error("删除百炼文件异常", e);
    }
}
```

## 5. 实际应用场景

### 5.1 身份证识别

**应用场景**：用户上传身份证照片，自动提取个人信息填充表单

**实现流程**：
```java
// 1. 构建身份证识别请求
FileClassifyDTO idCardRequest = FileClassifyDTO.builder()
        .userId("user123")
        .recognizeContent("姓名,身份证号,出生日期,性别,民族,住址")
        .fileUrls(Arrays.asList(idCardFile))
        .build();

// 2. 调用提取接口
JSONArray result = mattersClassifyService.withdrawBl(idCardRequest, FieldExtractionKey);

// 3. 处理提取结果
if (result != null && !result.isEmpty()) {
    JSONObject extractedData = result.getJSONObject(0);
    // 更新表单字段
    updateFormFields(extractedData);
}
```

### 5.2 营业执照识别

**应用场景**：企业用户上传营业执照，自动提取企业信息

**字段提取**：
- 企业名称
- 统一社会信用代码
- 法定代表人
- 注册资本
- 经营范围
- 注册地址

### 5.3 材料完整性检查

**应用场景**：检查用户上传的材料是否完整，缺少哪些必需材料

**实现逻辑**：
```java
public boolean checkMaterialCompleteness(String instanceId) {
    // 1. 获取必需材料清单
    List<BizInstanceMaterialVO> requiredMaterials = bizInstanceMaterialService.getRequiredMaterials(instanceId);
    
    // 2. 获取已上传材料
    List<BizInstanceMaterialVO> uploadedMaterials = bizInstanceMaterialService.getUploadedMaterials(instanceId);
    
    // 3. 检查完整性
    for (BizInstanceMaterialVO required : requiredMaterials) {
        boolean found = uploadedMaterials.stream()
                .anyMatch(uploaded -> uploaded.getMaterialId().equals(required.getMaterialId()));
        if (!found) {
            return false; // 缺少必需材料
        }
    }
    
    return true; // 材料完整
}
```

## 6. 最佳实践

### 6.1 文件处理优化

**文件大小控制**：
```java
// 检查文件大小
private boolean validateFileSize(FileDTO file) {
    long maxSize = 10 * 1024 * 1024; // 10MB
    if (file.getSize() > maxSize) {
        log.warn("文件大小超过限制: {}", file.getName());
        return false;
    }
    return true;
}

// 图片压缩
private byte[] compressImage(byte[] imageBytes) {
    // 实现图片压缩逻辑
    return compressedBytes;
}
```

**批量处理**：
```java
// 批量上传文件到百炼平台
public List<String> batchUploadFiles(List<FileDTO> files) {
    List<String> baiLianIds = new ArrayList<>();

    // 使用线程池并行处理
    List<CompletableFuture<String>> futures = files.stream()
            .map(file -> CompletableFuture.supplyAsync(() -> uploadFileBl(file, baiLianKey, baiLianUploadUrl)))
            .toList();

    // 等待所有上传完成
    for (CompletableFuture<String> future : futures) {
        try {
            String baiLianId = future.get(30, TimeUnit.SECONDS);
            if (baiLianId != null) {
                baiLianIds.add(baiLianId);
            }
        } catch (Exception e) {
            log.error("文件上传失败", e);
        }
    }

    return baiLianIds;
}
```

### 6.2 错误处理和重试

**重试机制**：
```java
@Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
public String uploadFileWithRetry(FileDTO file) {
    try {
        return uploadFileBl(file, baiLianKey, baiLianUploadUrl);
    } catch (Exception e) {
        log.warn("文件上传失败，准备重试: {}", file.getName());
        throw e;
    }
}

@Recover
public String recoverUpload(Exception ex, FileDTO file) {
    log.error("文件上传最终失败: {}", file.getName(), ex);
    return null;
}
```

**异常处理**：
```java
public ResponseData<Object> withdrawBlWithErrorHandling(FileClassifyDTO dto) {
    try {
        // 参数验证
        if (dto.getFileUrls() == null || dto.getFileUrls().isEmpty()) {
            return ResponseData.error("文件列表不能为空");
        }

        if (StringUtils.isBlank(dto.getRecognizeContent())) {
            return ResponseData.error("识别字段不能为空");
        }

        // 执行提取
        JSONArray result = mattersClassifyService.withdrawBl(dto, FieldExtractionKey);

        if (result == null || result.isEmpty()) {
            return ResponseData.error("未能提取到有效信息");
        }

        return ResponseData.success().data(result.getFirst());

    } catch (Exception e) {
        log.error("文件字段提取失败", e);
        return ResponseData.error("文件处理失败: " + e.getMessage());
    }
}
```

### 6.3 性能监控

**关键指标监控**：
```java
@Component
public class FileProcessingMonitor {

    private final MeterRegistry meterRegistry;

    public FileProcessingMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public void recordFileUpload(String fileType, long duration, boolean success) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("file.upload.duration")
                .tag("type", fileType)
                .tag("success", String.valueOf(success))
                .register(meterRegistry));
    }

    public void recordExtractionAccuracy(String fileType, double accuracy) {
        Gauge.builder("file.extraction.accuracy")
                .tag("type", fileType)
                .register(meterRegistry, accuracy);
    }
}
```

## 7. 调试和故障排除

### 7.1 常见问题

**文件上传失败**：
- 检查百炼API Key是否正确
- 验证文件URL是否可访问
- 确认文件格式是否支持

**字段提取不准确**：
- 优化recognizeContent描述
- 检查文件清晰度
- 调整AI模型参数

**性能问题**：
- 实现文件缓存机制
- 使用异步处理
- 优化图片压缩

### 7.2 日志监控

**关键日志点**：
```java
@Slf4j
public class FileProcessingLogger {

    public void logFileUpload(FileDTO file, String baiLianId) {
        log.info("文件上传成功: fileName={}, fileId={}, baiLianId={}",
                file.getName(), file.getId(), baiLianId);
    }

    public void logExtractionResult(String userId, int fieldCount, long duration) {
        log.info("字段提取完成: userId={}, fieldCount={}, duration={}ms",
                userId, fieldCount, duration);
    }

    public void logError(String operation, String fileId, Exception e) {
        log.error("文件处理失败: operation={}, fileId={}, error={}",
                operation, fileId, e.getMessage(), e);
    }
}
```

## 8. 总结

通过本指南，开发者可以：

1. **理解文件分类机制**：掌握AI驱动的智能文件识别和分类
2. **使用内容提取功能**：集成百炼AI实现字段自动提取
3. **调用相关API接口**：正确使用文件处理和提取接口
4. **应用实际场景**：实现身份证、营业执照等证件的智能识别
5. **优化性能和错误处理**：提升系统稳定性和用户体验

文件分类和提取功能为AI政务智能中枢平台提供了强大的智能化能力，大幅提升了用户办事效率和体验。
```
