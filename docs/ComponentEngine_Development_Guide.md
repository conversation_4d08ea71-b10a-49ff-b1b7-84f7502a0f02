# AI政务智能中枢平台 - 组件引擎开发指南

## 概述

组件引擎是AI政务智能中枢平台的核心架构，负责处理各种业务组件的调用、渲染、数据回填和流程流转。本指南详细介绍组件的完整生命周期开发。

## 1. 组件调用机制

### 1.1 组件工厂选择逻辑

`ComponentEngineFactory` 负责根据上下文选择合适的组件引擎：

```java
public Optional<ComponentEngineInterface> getEngine(ComponentDataContext context) {
    // 遍历所有引擎，依赖canHandle方法的高效实现
    for (AbstractComponentEngine engine : allEngines) {
        if (engine.canHandle(context)) {
            return Optional.of(engine);
        }
    }
    return Optional.empty();
}
```

### 1.2 上下文构建

`ComponentDataContext` 包含组件执行所需的所有信息：

```java
@Data
public class ComponentDataContext {
    private String recordId;        // 全局记录ID
    private String instanceId;      // 实例ID
    private ConfComponent confComponent;  // 组件配置
    private Object submitData;      // 提交数据
    private Map<String, Object> inputs;   // 输入参数
}
```

上下文构建过程：
```java
private ComponentDataContext getComponentDataContext(ComponentRunDTO componentRunDto) {
    ConfComponent component = confComponentService.getByCode(componentRunDto.getComponentCode());
    ComponentDataContext componentDataContext = new ComponentDataContext();
    componentDataContext.setConfComponent(component);
    componentDataContext.setRecordId(componentRunDto.getRecordId());
    componentDataContext.setInputs(componentRunDto.getInputs());
    
    // 获取缓存的流程信息，用于获取实例ID
    if (StringUtils.isBlank(componentRunDto.getInstanceId())) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentRunDto.getRecordId());
        componentRunDto.setInstanceId(chatProcessDTO.getInstanceId());
    }
    componentDataContext.setInstanceId(componentRunDto.getInstanceId());
    componentDataContext.setSubmitData(componentRunDto.getSubmitData());
    return componentDataContext;
}
```

### 1.3 组件选择规范

每个组件必须实现 `canHandle()` 方法来判断是否能处理当前上下文：

```java
@Override
public boolean canHandle(ComponentDataContext context) {
    return CODE.equals(context.getConfComponent().getEngineCode());
}
```

**特殊业务组件的选择逻辑**（如充电桩情形组件）：
```java
@Override
public boolean canHandle(ComponentDataContext context) {
    BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(context.getInstanceId());
    return super.canHandle(context) && "cdzbgyjs".equals(bizInstanceInfo.getMatterCode());
}
```

## 2. 组件渲染流程

### 2.1 执行流程

组件执行采用模板方法模式，`AbstractComponentEngine` 定义统一流程：

```java
public final ComponentRunVO execute(ComponentDataContext componentDataContext) {
    enrichContext();  // 充实上下文
    ComponentRunVO doneExecute = doExecute(componentDataContext);  // 执行业务逻辑
    setBasicProperties(doneExecute, componentDataContext);  // 设置基本属性
    return doneExecute;
}
```

### 2.2 ComponentRunVO 结构

组件渲染结果的标准格式：

```java
@Data
public class ComponentRunVO {
    Object metadata;                    // 元数据
    List<RenderData> renderData;        // 渲染数据列表
    String componentCode;               // 组件编码
    String engineCode;                  // 引擎编码
    String instanceId;                  // 组件实例ID
    String tips;                        // 提示信息
    
    @Data
    @Builder
    public static class RenderData {
        String componentName;           // 组件名称
        String componentCnName;         // 组件中文名称
        Object componentInfo;           // 组件信息
    }
}
```

### 2.3 渲染数据构建示例

**情境选择组件**：
```java
@Override
protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
    // 调用API获取数据
    ResponseData<List<ConfMatterAcceptQuotaVO>> listResponseData =
            bizInstanceSituationApi.get(componentDataContext.getInstanceId());
    List<ConfMatterAcceptQuotaVO> data = listResponseData.getData();

    // 封装返回数据
    ComponentRunVO componentRunVO = new ComponentRunVO();
    ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
            .componentName(CODE)
            .componentInfo(data)
            .build();
    componentRunVO.setRenderData(List.of(renderData));
    return componentRunVO;
}
```

**验证组件**：
```java
@Override
protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
    // 获取实例信息
    BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());
    List<BizInstanceMaterialGroupVO> materialGroup =
            bizInstanceMaterialService.getMaterialGroup(componentDataContext.getInstanceId());

    VerificationData verificationData = VerificationData.builder()
            .instanceId(componentDataContext.getInstanceId())
            .matterName(bizInstanceInfo.getMatterName())
            .hasMaterialGroup(!materialGroup.isEmpty())
            .build();

    ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
            .componentName(CODE)
            .componentInfo(verificationData)
            .build();

    ComponentRunVO vo = new ComponentRunVO();
    vo.setRenderData(Collections.singletonList(renderData));
    vo.setTips(getPropertyValue(componentDataContext, "tips"));
    return vo;
}
```

### 2.4 多组件渲染

某些组件可能需要渲染多个子组件：

```java
List<ComponentRunVO.RenderData> renderDataList = new ArrayList<>();

// 主组件数据
ComponentRunVO.RenderData mainRenderData = ComponentRunVO.RenderData.builder()
        .componentName("MainComponent")
        .componentInfo(mainData)
        .build();
renderDataList.add(mainRenderData);

// 附件组件数据
if (!fileVOS.isEmpty()) {
    ComponentRunVO.RenderData attachmentRenderData = ComponentRunVO.RenderData.builder()
            .componentName("AttachedFiles")
            .componentInfo(fileVOS)
            .build();
    renderDataList.add(attachmentRenderData);
}

ComponentRunVO vo = new ComponentRunVO();
vo.setRenderData(renderDataList);
```

## 3. 数据回填处理

### 3.1 回填机制

数据回填通过 `fillData()` 方法实现，处理用户提交的数据：

```java
public void backFillData(ComponentRunDTO componentRunDto) {
    ComponentDataContext componentDataContext = getComponentDataContext(componentRunDto);
    getEngine(componentDataContext)
            .ifPresent(engine -> engine.fillData(componentDataContext));
}
```

### 3.2 表单数据回填示例

**字段展示组件回填**：
```java
@Override
public void fillData(ComponentDataContext componentDataContext) {
    Object submitData = componentDataContext.getSubmitData();
    @SuppressWarnings("unchecked")
    Map<String, Object> submitDataMap = JSON.parseObject(submitData.toString(), Map.class);
    
    if (submitDataMap != null) {
        BizInstanceFields bizInstanceFields = bizInstanceFieldsService.
                queryForSingle(MapUtil.<String, Object>builder()
                    .put("=(instance.id)", componentDataContext.getInstanceId()).build());
        String formObj = bizInstanceFields.getFormObj();
        
        if (formObj != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
            formObjMap.putAll(submitDataMap);  // 合并提交数据
            bizInstanceFields.setFormObj(JSON.toJSONString(formObjMap));
            bizInstanceFieldsService.update(bizInstanceFields);
        }
    }
}
```

### 3.3 表单字段过滤

使用 `FormFieldFilterUtil` 进行表单字段过滤：

```java
// 获取表单字段过滤结果
FormFieldFilterUtil.FormFilterResult filterResult = FormFieldFilterUtil.filterFormWithIndexTracking(
        bizInstanceFieldsVO.getFormJson(),
        JSON.toJSONString(chatProcessDTO.getAiExtractFields()),
        false // 移除模式：显示需要用户填写的字段
);
```

**过滤模式说明**：
- **保留模式** (`true`)：保留AI已提取的字段，用于展示
- **移除模式** (`false`)：移除AI已提取的字段，显示需要用户填写的字段

**组件过滤模式选择**：
```java
// 根据组件类型决定过滤模式
public static boolean shouldUseKeepMode(String engineCode) {
    // FieldShow组件使用保留模式
    // FormGroup组件使用移除模式
    return ComponentEngineCode.shouldUseKeepMode(engineCode);
}
```

## 4. 流程流转控制

### 4.1 下一个组件选择

`ComponentFlowService` 负责获取流程中的下一个组件：

```java
public ConfComponent getNextComponent(ComponentDataContext currentComponent) {
    ChatProcessDTO chatProcessDTO = chatCacheUtil.get(currentComponent.getRecordId());
    String flowCode = chatProcessDTO.getFlowCode();
    List<ConfFlowDTO.ConfComponentDTO> components = confFlowApi.queryByCode(flowCode)
            .getData()
            .getComponents();

    for (ConfFlowDTO.ConfComponentDTO component : components) {
        if (component.getComponentId().equals(currentComponent.getConfComponent().getId())) {
            int nextIndex = components.indexOf(component) + 1;
            if (nextIndex < components.size()) {
                ConfFlowDTO.ConfComponentDTO confComponentDTO = components.get(nextIndex);
                return confComponentService.getByCode(confComponentDTO.getComponentCode());
            }
        }
    }
    return null;
}
```

### 4.2 流程指令控制

组件可以通过 `getNextInstruction()` 方法控制流程走向：

```java
@Override
public String getNextInstruction(ComponentDataContext componentDataContext) {
    // 检查是否还有字段需要填写
    if (!FormStepProcessor.hasFieldsToFill(filterResult.getFilteredFormJson(), 
                                          bizInstanceFieldsVO.getFieldsFilterMap())) {
        return InstructionConstant.SUCCESS_NEXT.getCode();  // 跳转到下一个组件
    }
    return super.getNextInstruction(componentDataContext);  // 默认结束
}
```

**常用指令常量**：
- `InstructionConstant.SUCCESS_END.getCode()` - 成功结束
- `InstructionConstant.SUCCESS_NEXT.getCode()` - 跳转到下一个组件

### 4.3 组件间数据传递

组件间通过以下方式传递数据：

1. **缓存机制**：通过 `ChatCacheUtil` 存储流程数据
2. **实例数据**：通过 `BizInstanceInfo` 存储业务实例数据
3. **表单数据**：通过 `BizInstanceFields` 存储表单填写数据

```java
// 获取缓存的流程数据
ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
Map<String, Object> aiExtractFields = chatProcessDTO.getAiExtractFields();

// 获取实例业务数据
BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());

// 获取表单数据
BizInstanceFieldsVO bizInstanceFieldsVO = 
    bizInstanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId());
```

## 5. 开发新组件的步骤

### 5.1 创建组件引擎类

```java
@Service
@Order(10) // 设置优先级，数字越小优先级越高
public class CustomComponentEngine extends AbstractComponentEngine {
    
    private static final String CODE = ComponentEngineCode.CUSTOM_COMPONENT;
    
    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        // 实现具体业务逻辑
        return componentRunVO;
    }
    
    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }
    
    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 实现数据回填逻辑
    }
    
    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        // 实现流程控制逻辑
        return super.getNextInstruction(componentDataContext);
    }
}
```

### 5.2 在常量类中定义组件编码

需要和前端约定好组件编码

```java
// 在 ComponentEngineCode 类中添加
public static final String CUSTOM_COMPONENT = "CustomComponent";
```

### 5.3 配置组件优先级

使用 `@Order` 注解控制组件选择优先级：
- 特定业务组件：1-20
- 默认实现组件：80-99  
- 抽象基类：100+

## 6. 组件属性配置详解

### 6.1 属性配置架构

组件属性配置采用两层架构，支持灵活的配置覆盖机制：

**配置来源优先级**：
1. **扩展属性** (`extendVO.componentProps`) - 最高优先级，业务特定配置
2. **默认配置** (`ConfComponent.content`) - 基础配置，组件默认行为

### 6.2 属性数据结构

#### 6.2.1 ComponentPropsDTO 结构

```java
@Schema(description = "组件属性DTO")
public class ComponentPropsDTO {
    @Schema(description = "组件名称")
    private String componentName;

    @Schema(description = "组件编码")
    private String componentCode;

    @Schema(description = "排序")
    private int sequenceOrder;

    @Schema(description = "属性列表")
    private List<PropsVO> props;

    public static class PropsVO {
        @Schema(description = "属性标题")
        private String label;

        @Schema(description = "属性名")
        private String name;

        @Schema(description = "属性值")
        private String value;
    }
}
```

#### 6.2.2 配置JSON格式示例

**默认配置** (ConfComponent.content)：
```json
{
    "tips": "默认提示信息",
    "title": "默认标题",
    "showButton": true,
    "maxItems": 10,
    "apiUrl": "https://api.example.com/default"
}
```

**扩展属性配置** (extendVO.componentProps)：
```json
[
    {
        "componentName": "情境选择组件",
        "componentCode": "ScenarioSelection",
        "sequenceOrder": 1,
        "props": [
            {
                "label": "提示信息",
                "name": "tips",
                "value": "请选择您的具体情况"
            },
            {
                "label": "标题",
                "name": "title",
                "value": "业务情境选择"
            },
            {
                "label": "最大选项数",
                "name": "maxItems",
                "value": "5"
            }
        ]
    }
]
```

### 6.3 属性获取方法详解

#### 6.3.1 基础属性获取

```java
// 获取字符串属性（推荐使用）
String tips = getPropertyValue(componentDataContext, "tips");

// 获取字符串属性（带默认值）
String title = getPropertyValue(componentDataContext, "title", "默认标题");

// 检查属性是否存在
boolean hasTips = hasProperty(componentDataContext, "tips");
```

#### 6.3.2 类型化属性获取

```java
// 获取布尔值属性
Boolean showButton = getPropertyValue(componentDataContext, "showButton", Boolean.class, true);

// 获取整数属性
Integer maxItems = getPropertyValue(componentDataContext, "maxItems", Integer.class, 10);

// 获取长整数属性
Long timeout = getPropertyValue(componentDataContext, "timeout", Long.class, 30000L);

// 获取双精度属性
Double rate = getPropertyValue(componentDataContext, "rate", Double.class, 1.0);
```

#### 6.3.3 高级属性操作

```java
// 获取所有属性的Map
Map<String, Object> allProps = componentPropsManager.getAllProperties(componentDataContext);

// 获取完整的合并配置
JSONObject mergedConfig = componentPropsManager.getMergedComponentProps(componentDataContext);

// 获取扩展属性（仅来自extendVO）
Optional<ComponentPropsDTO> extendProps = componentPropsManager.getExtendComponentProps(componentDataContext);
```

### 6.4 属性配置合并机制

#### 6.4.1 合并逻辑

`ComponentPropsManager` 的合并过程：

```java
public JSONObject getMergedComponentProps(ComponentDataContext componentDataContext) {
    // 1. 获取默认配置（content字段）
    JSONObject defaultConfig = getDefaultConfig(confComponent);

    // 2. 获取扩展属性（extendVO中的配置）
    Optional<ComponentPropsDTO> extendProps = getExtendComponentProps(componentDataContext);

    // 3. 合并配置：扩展属性覆盖默认配置
    return mergeConfigs(defaultConfig, extendProps);
}
```

#### 6.4.2 合并示例

**默认配置**：
```json
{
    "tips": "默认提示",
    "title": "默认标题",
    "maxItems": 10,
    "showButton": true
}
```

**扩展属性**：
```json
{
    "tips": "自定义提示",
    "maxItems": "5"
}
```

**合并结果**：
```json
{
    "tips": "自定义提示",      // 被扩展属性覆盖
    "title": "默认标题",       // 保持默认值
    "maxItems": "5",          // 被扩展属性覆盖
    "showButton": true        // 保持默认值
}
```

### 6.5 实际使用示例

#### 6.5.1 在组件中使用属性

```java
@Service
public class CustomComponentEngine extends AbstractComponentEngine {

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        // 获取配置属性
        String tips = getPropertyValue(componentDataContext, "tips", "默认提示");
        String title = getPropertyValue(componentDataContext, "title");
        Boolean showButton = getPropertyValue(componentDataContext, "showButton", Boolean.class, true);
        Integer maxItems = getPropertyValue(componentDataContext, "maxItems", Integer.class, 10);

        // 根据配置构建业务逻辑
        if (showButton) {
            // 显示按钮逻辑
        }

        // 限制返回数据数量
        List<DataItem> limitedData = data.stream()
                .limit(maxItems)
                .collect(Collectors.toList());

        // 构建渲染数据
        ComponentRunVO componentRunVO = new ComponentRunVO();
        componentRunVO.setTips(tips);

        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(limitedData)
                .build();
        componentRunVO.setRenderData(List.of(renderData));

        return componentRunVO;
    }
}
```

#### 6.5.2 动态配置验证

```java
@Override
protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
    // 验证必需属性
    if (!hasProperty(componentDataContext, "apiUrl")) {
        throw new IllegalArgumentException("缺少必需的配置属性: apiUrl");
    }

    // 获取并验证数值范围
    Integer maxItems = getPropertyValue(componentDataContext, "maxItems", Integer.class, 10);
    if (maxItems <= 0 || maxItems > 100) {
        log.warn("maxItems配置超出有效范围，使用默认值: {}", maxItems);
        maxItems = 10;
    }

    // 业务逻辑...
}
```

### 6.6 配置最佳实践

#### 6.6.1 属性命名规范

```java
// 推荐的属性命名
"tips"           // 提示信息
"title"          // 标题
"apiUrl"         // API地址
"maxItems"       // 最大项目数
"showButton"     // 是否显示按钮
"timeout"        // 超时时间
"enableCache"    // 是否启用缓存
```

#### 6.6.2 类型转换安全

```java
// 安全的类型转换
try {
    Integer value = getPropertyValue(componentDataContext, "maxItems", Integer.class, 10);
} catch (Exception e) {
    log.warn("属性类型转换失败，使用默认值: {}", e.getMessage());
    Integer value = 10;
}
```

#### 6.6.3 配置缓存优化

```java
// 在组件初始化时缓存配置
private Map<String, Object> configCache;

@Override
protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
    if (configCache == null) {
        configCache = componentPropsManager.getAllProperties(componentDataContext);
    }

    String tips = (String) configCache.getOrDefault("tips", "默认提示");
    // 使用缓存的配置...
}
```

## 7. 最佳实践

### 7.1 组件属性获取

推荐使用 `ComponentPropsManager` 获取组件配置：

```java
// 获取配置值
String tips = getPropertyValue(componentDataContext, "tips");
String customValue = getPropertyValue(componentDataContext, "customKey", "defaultValue");
```

### 6.2 错误处理

```java
@Override
protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
    try {
        // 业务逻辑
        return componentRunVO;
    } catch (Exception e) {
        log.error("组件执行失败", e);
        throw new RuntimeException("组件执行失败: " + e.getMessage());
    }
}
```

### 6.3 日志记录

```java
@Slf4j
@Service
public class CustomComponentEngine extends AbstractComponentEngine {
    
    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        log.info("CustomComponentEngine execute, instanceId: {}", 
                componentDataContext.getInstanceId());
        // 业务逻辑
        return componentRunVO;
    }
}
```

### 6.4 性能优化

- 避免在 `canHandle()` 方法中进行复杂计算
- 合理使用缓存机制
- 批量处理数据库操作

## 7. 调试和测试

### 7.1 组件调试

1. 检查 `canHandle()` 方法是否正确匹配
2. 验证 `ComponentDataContext` 数据完整性
3. 确认 `ComponentRunVO` 结构正确

### 7.2 常见问题

1. **组件未被选中**：检查 `canHandle()` 逻辑和 `@Order` 优先级
2. **数据回填失败**：检查 `fillData()` 方法中的数据解析逻辑
3. **流程跳转异常**：检查 `getNextInstruction()` 返回值
4. **属性获取失败**：检查属性名称拼写和配置格式
5. **类型转换错误**：确保属性值格式与目标类型匹配

### 7.3 配置调试技巧

```java
// 调试属性配置
@Override
protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
    // 打印所有配置信息
    Map<String, Object> allProps = componentPropsManager.getAllProperties(componentDataContext);
    log.debug("组件配置信息: {}", JSON.toJSONString(allProps));

    // 打印合并后的配置
    JSONObject mergedConfig = componentPropsManager.getMergedComponentProps(componentDataContext);
    log.debug("合并后配置: {}", mergedConfig.toJSONString());

    // 检查扩展属性
    Optional<ComponentPropsDTO> extendProps = componentPropsManager.getExtendComponentProps(componentDataContext);
    if (extendProps.isPresent()) {
        log.debug("扩展属性: {}", JSON.toJSONString(extendProps.get()));
    } else {
        log.debug("未找到扩展属性配置");
    }

    // 业务逻辑...
}
```

## 8. 总结

通过本指南，开发者可以：

1. **理解组件架构**：掌握组件引擎的调用、渲染、回填和流转机制
2. **熟练使用属性配置**：灵活运用两层配置架构，实现业务定制
3. **快速开发组件**：按照标准模板创建新的组件引擎
4. **优化组件性能**：采用最佳实践提升组件执行效率
5. **调试组件问题**：使用调试技巧快速定位和解决问题

组件引擎为AI政务智能中枢平台提供了强大的扩展能力，支持复杂业务流程的灵活处理和定制化配置。
