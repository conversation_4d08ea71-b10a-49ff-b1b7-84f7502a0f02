# AI政务智能中枢平台 - 对话接口开发指南

## 概述

对话接口是AI政务智能中枢平台的核心交互入口，负责处理用户与AI智能体的对话交互、组件运行、数据回填和对话记录管理。本指南详细介绍对话接口的使用方法、参数传递和对话记录机制。

## 1. 对话接口架构

### 1.1 核心接口列表

```java
@Tag(name = "对话")
@RestController
@RequestMapping(value = "/api/chat")
public interface ChatApi {
    
    // 组件运行接口
    @PostMapping("/component/run")
    ResponseData<ComponentRunVO> componentRun(@RequestBody ComponentRunDTO componentRunDto);
    
    // 流式对话接口
    @PostMapping("/ask")
    Flux<ServerSentEvent<String>> ask(@RequestBody AskDTO ask);
    
    // 阻塞式对话接口
    @PostMapping("/ask/block")
    ResponseData<Object> askBlock(@RequestBody AskDTO ask);
    
    // 对话记录相关接口
    @GetMapping("/conversation/messages")
    ResponseData<List<BizChatMessageVO>> getConversationMessages(BizChatMessageDTO dto);
    
    // 文件上传更新接口
    @PostMapping("/file/upload")
    ResponseData<Void> updateFileUploadList(@RequestBody UpdateFileUploadDTO updateFileUploadDTO);
    
    // 停止对话接口
    @PostMapping("/stop")
    ResponseData<Void> stopChat(@RequestBody StopChatDTO stopChatDTO);
}
```

### 1.2 接口分类

**核心对话接口**：
- `ask` - 流式对话，支持实时响应
- `askBlock` - 阻塞式对话，等待完整响应
- `componentRun` - 组件运行，处理业务组件

**对话管理接口**：
- `getConversationMessages` - 获取对话历史
- `updateFileUploadList` - 更新文件上传
- `stopChat` - 停止对话流

## 2. 核心数据结构

### 2.1 AskDTO - 对话请求参数

```java
@Data
public class AskDTO {
    @Schema(description = "用户id")
    private String userId;              // 必需：用户唯一标识
    
    @Schema(description = "会话id")
    private String chatId;              // 可选：智能体会话ID
    
    @Schema(description = "问题")
    private String questions;           // 必需：用户输入的问题
    
    @Schema(description = "inputs")
    private Map<String, Object> inputs; // 可选：额外输入参数
    
    @Schema(description = "appKey")
    private String appKey;              // 可选：智能体应用Key
    
    @Schema(description = "全局记录id")
    private String recordId;            // 可选：全局会话记录ID
    
    @Schema(description = "文件数据")
    private List<FileDTO> fileUrls;     // 可选：上传的文件列表
    
    @Schema(description = "组件运行DTO")
    private ComponentRunDTO componentRunDto; // 可选：组件运行数据
}
```

### 2.2 ComponentRunDTO - 组件运行参数

```java
@Data
public class ComponentRunDTO {
    @Schema(description = "组件编码")
    private String componentCode;       // 必需：组件编码
    
    @Schema(description = "引擎编码")
    private String engineCode;          // 可选：引擎编码
    
    @Schema(description = "实例ID")
    private String instanceId;          // 必需：业务实例ID
    
    @Schema(description = "全局记录id")
    private String recordId;            // 必需：全局记录ID
    
    @Schema(description = "渲染数据")
    private Object renderData;          // 可选：渲染数据
    
    @Schema(description = "提交数据")
    private Object submitData;          // 可选：用户提交的数据
    
    @Schema(description = "输入数据")
    private Map<String, Object> inputs; // 可选：输入参数
}
```

### 2.3 ChatProcessDTO - 对话流程上下文

```java
@Data
public class ChatProcessDTO {
    @Schema(description = "全局记录id")
    private String recordId;            // 全局会话记录ID
    
    @Schema(description = "实例id")
    private String instanceId;          // 业务实例ID
    
    @Schema(description = "事项名称")
    private String matterName;          // 办理事项名称
    
    @Schema(description = "分发智能体key")
    private String agentKey;            // 智能体Key
    
    @Schema(description = "流程code")
    private String flowCode;            // 业务流程编码
    
    @Schema(description = "材料上传列表")
    private List<BizInstanceMaterialVO> materialSubmitVOS; // 材料上传列表
    
    @Schema(description = "上传文件列表")
    private List<FileDTO> fileUrls;     // 文件上传列表
    
    @Schema(description = "表单分组当前数")
    private Integer formStepIndex;      // 当前表单步骤
    
    @Schema(description = "表单分组总数")
    private Integer formStepCount;      // 表单总步骤数
    
    @Schema(description = "AI提取字段")
    private Map<String, Object> aiExtractFields; // AI提取的字段数据
    
    @Schema(description = "签名文件id")
    private String signFileId;          // 签名文件ID
    
    @Schema(description = "签名状态")
    private String signStatus;          // 签名状态
}
```

## 3. 接口使用详解

### 3.1 流式对话接口 - /api/chat/ask

**功能**：支持实时流式响应的对话接口，适用于需要实时反馈的场景。

**请求示例**：
```json
{
    "userId": "user123",
    "questions": "我要办理营业执照",
    "inputs": {
        "channel": "web",
        "location": "苏州市"
    },
    "recordId": "record_20250801_001"
}
```

**响应格式**：Server-Sent Events (SSE) 流式数据
```
data: {"event":"message","content":"正在为您查询营业执照办理流程...","taskId":"task123"}

data: {"event":"message","content":"已找到相关流程，请提供以下信息...","taskId":"task123"}

data: {"event":"message_end","conversationId":"conv123","taskId":"task123"}
```

**关键处理逻辑**：
```java
public Flux<ServerSentEvent<String>> ask(AskDTO ask) {
    // 1. 初始化或获取对话上下文
    ChatProcessDTO chatProcessDTO;
    if (ask.getRecordId() == null) {
        // 创建新会话
        BizChatConversation conversation = chatConversationService.createConversation(
            ask.getUserId(), ask.getQuestions(), ask.getChannel(), null);
        ask.setRecordId(conversation.getId());
        chatProcessDTO = new ChatProcessDTO();
    } else {
        // 获取已有会话
        chatProcessDTO = chatCacheUtil.get(ask.getRecordId());
    }
    
    // 2. 保存用户消息
    chatMessageService.saveUserMessage(ask.getRecordId(), ask.getQuestions());
    
    // 3. 处理组件数据回填
    if (ask.getComponentRunDto().getSubmitData() != null) {
        chatComponentService.BackFillData(ask.getComponentRunDto());
        ask.setQuestions(chatComponentService.getNextInstruction(ask.getComponentRunDto()));
    }
    
    // 4. 调用智能体流式接口
    return difyServiceHelper.streamingMessage(ask, appKey);
}
```

### 3.2 组件运行接口 - /api/chat/component/run

**功能**：执行特定的业务组件，获取组件渲染数据。

**请求示例**：
```json
{
    "componentCode": "ScenarioSelection",
    "instanceId": "inst_20250801_001",
    "recordId": "record_20250801_001",
    "submitData": {
        "selectedScenario": "individual_business",
        "businessType": "retail"
    },
    "inputs": {
        "step": 1
    }
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "componentCode": "ScenarioSelection",
        "engineCode": "ScenarioSelection",
        "instanceId": "inst_20250801_001",
        "tips": "请选择您的具体情况",
        "renderData": [
            {
                "componentName": "ScenarioSelection",
                "componentCnName": "情境选择",
                "componentInfo": [
                    {
                        "id": "scenario1",
                        "name": "个体工商户",
                        "description": "个人经营的商户"
                    },
                    {
                        "id": "scenario2", 
                        "name": "有限责任公司",
                        "description": "有限责任制企业"
                    }
                ]
            }
        ]
    }
}
```

### 3.3 阻塞式对话接口 - /api/chat/ask/block

**功能**：等待完整响应的对话接口，适用于需要完整结果的场景。

**请求示例**：
```json
{
    "userId": "user123",
    "questions": "定义业务办理流程{\"matterId\":\"matter123\",\"flowCode\":\"flow001\",\"matterName\":\"营业执照办理\"}",
    "appKey": "app_business_license"
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "event": "message",
        "messageId": "msg123",
        "conversationId": "conv123",
        "answer": "已为您初始化营业执照办理流程，请按照以下步骤进行...",
        "recordId": "record_20250801_001",
        "createdAt": 1722470400000
    }
}
```

## 4. 对话记录管理

### 4.1 对话记录保存机制

**自动保存**：
- 用户消息自动保存到数据库
- AI响应自动保存到数据库
- 组件运行结果自动保存

**保存逻辑**：
```java
public void saveUserMessage(String conversationId, String content) {
    // 过滤不需要记录的消息
    if (shouldIgnoreMessage(content)) return;
    
    // 发布消息事件
    ChatMessageEvent event = ChatMessageEvent.builder()
            .conversationId(conversationId)
            .content(content)
            .sender("USER")
            .timestamp(System.currentTimeMillis())
            .build();
    eventPublisher.publishEvent(event);
}

public void saveAiMessage(String conversationId, String content) {
    // 保存AI响应消息
    ChatMessageEvent event = ChatMessageEvent.builder()
            .conversationId(conversationId)
            .content(content)
            .sender("ASSISTANT")
            .timestamp(System.currentTimeMillis())
            .build();
    eventPublisher.publishEvent(event);
}
```

### 4.2 对话历史查询

**接口**：`GET /api/chat/conversation/messages`

**请求参数**：
```java
@Data
public class BizChatMessageDTO {
    @Schema(description = "会话id")
    String conversationId;      // 必需：会话ID
    
    @Schema(description = "问题")
    String question;            // 可选：问题内容
    
    @Schema(description = "答案")
    String answer;              // 可选：答案内容
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "conversationId": "conv123",
            "sender": "USER",
            "content": "我要办理营业执照",
            "createTime": "2025-08-01T10:00:00Z"
        },
        {
            "conversationId": "conv123",
            "sender": "ASSISTANT", 
            "content": "好的，我来为您办理营业执照。请先选择您的经营类型...",
            "createTime": "2025-08-01T10:00:05Z"
        }
    ]
}
```

### 4.3 对话缓存机制

**缓存工具**：`ChatCacheUtil`

**缓存操作**：
```java
@Component
public class ChatCacheUtil {
    private final String CHAT_PROCESS_KEY = "chat:process:data:";
    
    // 缓存对话流程数据（默认7天过期）
    public void set(String key, ChatProcessDTO chatProcessDTO) {
        redisUtil.set(CHAT_PROCESS_KEY + key, JSONObject.toJSONString(chatProcessDTO), 60 * 60 * 24 * 7);
    }
    
    // 获取对话流程数据
    public ChatProcessDTO get(String key) {
        return JSONObject.parseObject((String) redisUtil.get(CHAT_PROCESS_KEY + key), ChatProcessDTO.class);
    }
}
```

**缓存内容**：
- 对话流程状态
- AI提取字段
- 表单填写进度
- 材料上传状态
- 签名状态等

## 5. 参数传递机制

### 5.1 会话上下文传递

**recordId**：全局会话记录ID，贯穿整个对话流程
- 新会话：系统自动生成
- 继续会话：客户端传入已有recordId

**instanceId**：业务实例ID，标识具体的办件实例
- 初始化时创建
- 后续组件运行时使用

### 5.2 组件间数据传递

**通过ChatProcessDTO传递**：
```java
// 在组件中获取上下文数据
ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
Map<String, Object> aiExtractFields = chatProcessDTO.getAiExtractFields();

// 更新上下文数据
chatProcessDTO.setFormStepIndex(currentStep);
chatCacheUtil.set(recordId, chatProcessDTO);
```

**通过ComponentRunDTO传递**：
```java
// 组件提交数据
ComponentRunDTO componentRunDto = new ComponentRunDTO();
componentRunDto.setSubmitData(userInputData);
componentRunDto.setRecordId(recordId);
componentRunDto.setInstanceId(instanceId);
```

### 5.3 文件上传处理

**文件上传接口**：`POST /api/chat/file/upload`

**请求示例**：
```json
{
    "recordId": "record_20250801_001",
    "materialSubmitVOS": [
        {
            "materialId": "material123",
            "materialName": "身份证",
            "fileUrl": "https://example.com/files/idcard.jpg",
            "fileName": "身份证.jpg"
        }
    ]
}
```

**处理逻辑**：
```java
public ResponseData<Void> updateFileUploadList(UpdateFileUploadDTO updateFileUploadDTO) {
    // 获取对话上下文
    ChatProcessDTO chatProcessDTO = chatCacheUtil.get(updateFileUploadDTO.getRecordId());
    
    // 更新材料上传列表
    chatProcessDTO.setMaterialSubmitVOS(updateFileUploadDTO.getMaterialSubmitVOS());
    
    // 保存到缓存
    chatCacheUtil.set(updateFileUploadDTO.getRecordId(), chatProcessDTO);
    
    return ResponseData.success().build();
}
```

## 6. 错误处理和最佳实践

### 6.1 错误处理

**常见错误场景**：
1. recordId不存在或已过期
2. 组件编码不正确
3. 实例ID无效
4. 智能体服务不可用

**错误处理示例**：
```java
public ResponseData<ComponentRunVO> componentRun(ComponentRunDTO componentRunDto) {
    try {
        // 验证必需参数
        if (StringUtils.isBlank(componentRunDto.getRecordId())) {
            return ResponseData.error("recordId不能为空");
        }
        
        // 执行组件
        Object result = componentEngineFactory.run(componentRunDto);
        if (result instanceof ComponentRunVO componentRunVO) {
            return ResponseData.success(componentRunVO);
        }
        
        return ResponseData.error("组件执行失败");
    } catch (Exception e) {
        log.error("组件运行异常", e);
        return ResponseData.error("系统异常：" + e.getMessage());
    }
}
```

### 6.2 最佳实践

**参数验证**：
- 必需参数非空验证
- 参数格式验证
- 业务逻辑验证

**性能优化**：
- 合理使用缓存
- 避免重复查询
- 异步处理非关键操作

**安全考虑**：
- 用户身份验证
- 数据权限控制
- 敏感信息脱敏

## 7. 完整调用示例

### 7.1 新用户首次对话流程

```javascript
// 1. 发起首次对话
const askRequest = {
    userId: "user123",
    questions: "我要办理营业执照",
    inputs: {
        channel: "web",
        location: "苏州市"
    }
};

// 调用流式对话接口
fetch('/api/chat/ask', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(askRequest)
})
.then(response => {
    const reader = response.body.getReader();
    // 处理流式响应...
});
```

### 7.2 组件交互流程

```javascript
// 2. 用户选择情境后，运行组件
const componentRequest = {
    componentCode: "ScenarioSelection",
    recordId: "record_20250801_001",
    instanceId: "inst_20250801_001",
    submitData: {
        selectedScenario: "individual_business"
    }
};

// 调用组件运行接口
fetch('/api/chat/component/run', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(componentRequest)
})
.then(response => response.json())
.then(data => {
    console.log('组件运行结果:', data);
    // 渲染组件UI...
});
```

### 7.3 继续对话流程

```javascript
// 3. 基于组件结果继续对话
const continueAskRequest = {
    userId: "user123",
    recordId: "record_20250801_001", // 使用已有recordId
    questions: "我选择了个体工商户，下一步怎么办？",
    componentRunDto: {
        componentCode: "ScenarioSelection",
        recordId: "record_20250801_001",
        instanceId: "inst_20250801_001",
        submitData: {
            selectedScenario: "individual_business"
        }
    }
};

fetch('/api/chat/ask', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(continueAskRequest)
});
```

### 7.4 文件上传流程

```javascript
// 4. 上传材料文件
const fileUploadRequest = {
    recordId: "record_20250801_001",
    materialSubmitVOS: [
        {
            materialId: "material123",
            materialName: "身份证",
            fileUrl: "https://example.com/files/idcard.jpg",
            fileName: "身份证.jpg"
        }
    ]
};

fetch('/api/chat/file/upload', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(fileUploadRequest)
});
```

### 7.5 查询对话历史

```javascript
// 5. 获取对话历史记录
fetch(`/api/chat/conversation/messages?conversationId=conv123`)
.then(response => response.json())
.then(data => {
    console.log('对话历史:', data.data);
    // 渲染对话历史...
});
```

## 8. 调试和监控

### 8.1 日志监控

**关键日志点**：
- 对话请求接收
- 组件运行状态
- 缓存操作
- 智能体调用
- 错误异常

**日志示例**：
```java
@Slf4j
public class ChatApiImpl {

    @Override
    public ResponseData<ComponentRunVO> componentRun(ComponentRunDTO componentRunDto) {
        log.info("组件运行请求: componentCode={}, recordId={}, instanceId={}",
                componentRunDto.getComponentCode(),
                componentRunDto.getRecordId(),
                componentRunDto.getInstanceId());

        try {
            Object result = componentEngineFactory.run(componentRunDto);
            log.info("组件运行成功: componentCode={}", componentRunDto.getComponentCode());
            return ResponseData.success((ComponentRunVO) result);
        } catch (Exception e) {
            log.error("组件运行失败: componentCode={}, error={}",
                    componentRunDto.getComponentCode(), e.getMessage(), e);
            return ResponseData.error("组件运行失败");
        }
    }
}
```

### 8.2 性能监控

**关键指标**：
- 接口响应时间
- 组件执行时间
- 缓存命中率
- 智能体调用延迟

**监控示例**：
```java
@Component
public class ChatPerformanceMonitor {

    @EventListener
    public void handleComponentRun(ComponentRunEvent event) {
        long duration = event.getEndTime() - event.getStartTime();
        if (duration > 3000) { // 超过3秒记录警告
            log.warn("组件执行耗时过长: componentCode={}, duration={}ms",
                    event.getComponentCode(), duration);
        }
    }
}
```

## 9. 总结

通过本指南，开发者可以：

1. **理解对话架构**：掌握流式和阻塞式对话的使用场景
2. **熟练使用接口**：正确传递参数，处理响应数据
3. **管理对话状态**：有效利用缓存机制和上下文传递
4. **处理文件上传**：实现材料文件的上传和管理
5. **调试和监控**：快速定位问题，优化性能

对话接口为AI政务智能中枢平台提供了强大的交互能力，支持复杂业务流程的智能化处理和用户友好的交互体验。
