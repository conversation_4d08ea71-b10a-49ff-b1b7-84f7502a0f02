package com.workplat.accept.business.chat.dto;

import lombok.Data;

/**
 * @ClassName: FileRequestBody
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-02-25 16:11
 * @Version
 **/
@Data
public class FileDTO {

    /**
     * 文件id
     */
    private String id;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 文件url
     */
    private String url;

    /**
     * 文件备注
     */
    private String remark;

    /**
     * 文件百炼id
     */
    private String baiLianId;

    /**
     * 拷贝构造方法，用于深拷贝
     */
    public FileDTO(FileDTO other) {
        if (other != null) {
            this.id = other.id;
            this.name = other.name;
            this.type = other.type;
            this.url = other.url;
            this.remark = other.remark;
            this.baiLianId = other.baiLianId;
        }
    }

    /**
     * 默认构造方法
     */
    public FileDTO() {
    }
}
