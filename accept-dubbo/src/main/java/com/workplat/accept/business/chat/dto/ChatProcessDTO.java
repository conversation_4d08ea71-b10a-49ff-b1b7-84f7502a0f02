package com.workplat.accept.business.chat.dto;

import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class ChatProcessDTO {

    @Schema(description = "全局记录id")
    private String recordId;

    @Schema(description = "实例id")
    private String instanceId;

    @Schema(description = "事项名称")
    private String matterName;

    @Schema(description = "分发智能体key")
    private String agentKey;

    @Schema(description = "流程code")
    private String flowCode;

    @Schema(description = "材料上传列表")
    private List<BizInstanceMaterialVO> materialSubmitVOS = new ArrayList<>();

    @Schema(description = "上传文件列表")
    private List<FileDTO> fileUrls;

    @Schema(description = "表单分组当前数")
    private Integer formStepIndex;

    @Schema(description = "表单分组总数")
    private Integer formStepCount;

    @Schema(description = "AI提取字段")
    private Map<String, Object> aiExtractFields;

    @Schema(description = "签名文件id")
    private String signFileId;

    @Schema(description = "材料分组总数")
    private Integer materialGroupCount;

    @Schema(description = "材料分组当前数")
    private Integer materialGroupIndex;

    @Schema(description = "接口提取字段code")
    private Set<String> apiExtractCode;

    @Schema(description = "签名状态")
    private String signStatus;
}
