package com.workplat.accept.business.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Builder;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.util.List;


@Data
@Schema(name = "组件运行模型")
public class ComponentRunVO {


    @Schema(description = "元数据")
    Object metadata;

    @Schema(description = "渲染数据")
    List<RenderData> renderData;

    @Schema(description = "组件编码")
    String componentCode;

    @Schema(description = "引擎编码")
    private String engineCode;

    @Schema(description = "组件实例id")
    String instanceId;

    @Schema(description = "提示")
    String tips;

    @Data
    @Builder
    public static class RenderData {

        @Schema(description = "组件名称")
        String componentName;

        @Schema(description = "组件中文名称")
        String componentCnName;

        @Schema(description = "组件信息")
        Object componentInfo;
    }

}
