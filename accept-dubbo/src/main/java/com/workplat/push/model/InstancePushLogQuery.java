package com.workplat.push.model;

import com.workplat.gss.common.core.annotation.QueryMapBuild;
import com.workplat.gss.common.core.constant.QueryOperator;
import com.workplat.gss.common.core.dto.BaseQuery;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName: query
 * @Description:
 * @Author: Yang <PERSON>
 * @Date: 2025-07-10 20:24
 * @Version
 **/
@Setter
@Getter
public class InstancePushLogQuery extends BaseQuery {


    @QueryMapBuild(operator = QueryOperator.IN)
    private List<String> pushStatus;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "instance.matterName")
    private String matterName;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "instance.applicationName")
    private String applicationName;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "instance.applicationCertificateCode")
    private String applicationCertificateCode;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "instance.applicationPhone")
    private String applicationPhone;
}
