package com.workplat.push.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Schema(description = "办件推送日志VO")
public class InstancePushLogVO {

    @Schema(description = "主键id")
    private String id;

    @Schema(description = "办件ID")
    private String instanceId;

    @Schema(description = "事项名称")
    private String matterName;

    @Schema(description = "申请人姓名")
    private String applicationName;

    @Schema(description = "推送状态(0-未推送,1-推送成功,2-推送失败)")
    private String pushStatus;

    @Schema(description = "推送时间")
    private Date pushTime;

    @Schema(description = "推送结果")
    private String pushResult;
}
