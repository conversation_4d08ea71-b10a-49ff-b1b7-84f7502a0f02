package com.workplat.flow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR> cheng
 * @package com.workplat.flow.dto
 * @description
 * @date 2025/6/14 20:27
 */
@Data
public class ProgressPercentageDTO {

    @NotBlank(message = "userId不能为空")
    @Schema(description = "userId")
    private String userId;

    @NotBlank(message = "chatId不能为空")
    @Schema(description = "chatId")
    private String chatId;

    @NotBlank(message = "instanceId不能为空")
    @Schema(description = "instanceId")
    private String instanceId;

    @Schema(description = "访问渠道")
    private String channel;
}
