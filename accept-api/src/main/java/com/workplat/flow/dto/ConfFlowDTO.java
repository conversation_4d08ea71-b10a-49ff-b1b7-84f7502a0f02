package com.workplat.flow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ConfFlowSaveDTO
 * @Description
 * @Date 2025/5/22
 * @Version 1.0.0
 **/
@Data
public class ConfFlowDTO {

    @Schema(description = "id")
    private String id;

    @NotBlank(message = "流程名称不能为空")
    @Schema(description = "流程名称")
    private String name;

    @NotBlank(message = "流程编码不能为空")
    @Schema(description = "流程编码")
    private String code;

    @Schema(description = "流程描述")
    private String description;

    @Schema(description = "组件列表")
    private List<ConfComponentDTO> components;

    @Data
    public static class ConfComponentDTO {

        @Schema(description = "组件id")
        private String id;

        @Schema(description = "组件id")
        private String componentId;

        @Schema(description = "组件名称")
        private String componentName;

        @Schema(description = "组件编码")
        private String componentCode;

        @Schema(description = "排序")
        private int sequenceOrder;

        @Schema(description = "扩展")
        private String configuration;

        @Schema(description = "可替换属性", example = "{\"组件属性label\":\"组件存储key\"}")
        private String replaceableProps;
    }

}
