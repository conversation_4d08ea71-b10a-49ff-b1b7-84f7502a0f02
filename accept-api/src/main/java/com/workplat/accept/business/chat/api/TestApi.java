package com.workplat.accept.business.chat.api;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "对话")
@RestController
@RequestMapping(value = "/api/test")
public interface TestApi {


    @RequestMapping("/test")
    public String test();

    @RequestMapping("/test2")
    public JSONObject test2(String instanceId,Boolean isFirstSubmit);
    
    @RequestMapping("/testDistributionDriver")
    public JSONObject testDistributionDriver(String instanceId);
}
