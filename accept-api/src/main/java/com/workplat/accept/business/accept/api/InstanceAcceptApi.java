package com.workplat.accept.business.accept.api;

import com.workplat.accept.business.chat.dto.InstancePushLogDTO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.push.vo.InstancePushLogVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Odin
 * @Date: 2024/9/23 09:53
 * @Description:
 */

@Tag(name = "业务审批")
@RestController
@RequestMapping("/api/instance/accept")
public interface InstanceAcceptApi {

    @Operation(summary = "更改办件状态为草稿")
    @GetMapping("/updateDraft")
    ResponseData<String> updateDraft(String instanceId);

    @Operation(summary = "更改推送状态为待处理")
    @GetMapping("/updatePushStatus")
    ResponseData<String> updatePushStatus(String instanceId);

    @Operation(summary = "获取推送记录分页")
    @GetMapping("/pushLog/page")
    ResponseData<Page<InstancePushLogVO>> pushLogPage(InstancePushLogDTO dto, PageableDTO pageable);


    @Operation(summary = "社会救助研判")
    @GetMapping("/socialJudge")
        ResponseData<Object> socialJudge(String instanceId);
}
