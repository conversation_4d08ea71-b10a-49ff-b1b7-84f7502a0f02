package com.workplat.serve.api;


import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.serve.dto.BizServeInfoDto;
import com.workplat.serve.dto.IdsDto;
import com.workplat.serve.dto.KeyDto;
import com.workplat.accept.business.serve.vo.BizServeInfoVo;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.serve.dto.MethodDefaultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Tag(name = "服务相关接口")
@RestController
@RequestMapping(value = "/api/serve")
public interface BizServeApi {


    @Operation(summary = "服务列表")
    @PostMapping("/queryListByName")
    ResponseData<Page<BizServeInfoVo>> queryListByName(@RequestBody(required = false) KeyDto keyDto, PageableDTO pageDTO);

    @Operation(summary = "根据id查询服务")
    @GetMapping("/getById")
    ResponseData<BizServeInfoVo> getById(String id);

    @Operation(summary = "根据id和渠道查询服务(WX/PC)")
    @GetMapping("/getByIdToChannel")
    ResponseData<BizServeInfoVo> getByIdToChannel(String id, String channel);

    @Operation(summary = "根据ids查询服务")
    @GetMapping("/getByIds")
    ResponseData<List<BizServeInfoVo>> getByIds(String ids);

    @Operation(summary = "删除服务")
    @PostMapping("/deleteById")
    ResponseData  deleteInfo(@RequestBody BizServeInfoDto BizServeInfoDto);

    @Operation(summary = "修改服务")
    @PostMapping("/updateById")
    ResponseData updateById(@RequestBody BizServeInfoDto BizServeInfoDto);

    @Operation(summary = "新增服务")
    @PostMapping("/add")
    ResponseData addInfo(@RequestBody BizServeInfoDto bizServeInfoDto);

    @Operation(summary = "导入数据")
    @PostMapping("/importServe")
    ResponseData importData(MultipartFile file);

    @Operation(summary = "导出数据")
    @PostMapping("/exportServe")
    ResponseData exportData(@RequestBody IdsDto idsDto, HttpServletResponse response);

    @Operation(summary = "获取认证等级字典")
    @GetMapping("/getRZDJ")
    ResponseData getRZDJ();

    @Operation(summary = "获取服务类型字典")
    @GetMapping("/getFWLX")
    ResponseData getFWLX();

    @Operation(summary = "获取办事指南类型字典")
    @GetMapping("/getBSZNLX")
    ResponseData getBSZNLX();

    @Operation(summary = "获取渠道类型字典")
    @GetMapping("/getQDLX")
    ResponseData getQDLX();

    @Operation(summary = "批量启用")
    @PostMapping("/plqy")
    ResponseData plqy(@RequestBody IdsDto idsDto);

    @Operation(summary = "批量取消启用")
    @PostMapping("/plqxqy")
    ResponseData plqxqy(@RequestBody IdsDto idsDto);

    @Operation(summary = "保存服务方式默认值")
    @PostMapping("/saveMethodDefault")
    ResponseData<Void> saveMethodDefault(@RequestBody MethodDefaultDTO dto);

    @Operation(summary = "查询服务方式默认值")
    @GetMapping("/queryMethodDefault")
    ResponseData<List<MethodDefaultDTO>> queryMethodDefault(String method);

    @Operation(summary = "删除服务方式默认值")
    @GetMapping("/deleteMethodDefault")
    ResponseData<Void> deleteMethodDefault(String method);
}
