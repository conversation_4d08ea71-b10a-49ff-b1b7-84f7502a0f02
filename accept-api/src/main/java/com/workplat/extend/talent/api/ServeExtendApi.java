package com.workplat.extend.talent.api;

import com.workplat.extend.talent.vo.HouseRegVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> cheng
 * @package com.workplat.extend.api
 * @description
 * @date 2025/7/9 16:20
 */
@RestController
@RequestMapping("/api/matter/extend/")
@Tag(name = "边聊边办业务扩展接口模块")
public interface ServeExtendApi {

    @PostMapping("/saveHouseReg")
    @Operation(summary = "保存人才落户登记表")
    @ApiLogging(module = "边聊边办业务扩展接口模块", operation = "保存人才落户登记表", type = OperationType.INSERT)
    ResponseData<Void> saveHouseReg(@RequestBody String fileIds);

    @GetMapping("/queryHouseReg")
    @Operation(summary = "查询人才落户登记表")
    @ApiLogging(module = "边聊边办业务扩展接口模块", operation = "查询人才落户登记表", type = OperationType.QUERY)
    ResponseData<Page<HouseRegVO>> queryHouseReg(String registerName, String startTime, String endTime, PageableDTO pageDTO);

    @GetMapping("/deleteById")
    @Operation(summary = "删除人才落户登记表")
    @ApiLogging(module = "边聊边办业务扩展接口模块", operation = "删除人才落户登记表", type = OperationType.DELETE)
    ResponseData<Void> deleteById(String id);
}
