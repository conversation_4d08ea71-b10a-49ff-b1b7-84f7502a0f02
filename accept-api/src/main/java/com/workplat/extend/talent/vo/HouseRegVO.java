package com.workplat.extend.talent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> cheng
 * @package com.workplat.extend.vo
 * @description
 * @date 2025/7/10 9:30
 */
@Data
public class HouseRegVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "登记人姓名")
    private String registerName;

    @Schema(description = "登记人身份证号")
    private String registerCardId;

    @Schema(description = "登记时间")
    private Date registerTime;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件id")
    private String fileId;

    @Schema(description = "文件数据")
    private String dataJson;
}
