package com.workplat.extend.socialAssistance.api;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.extend.socialAssistance.vo.ReturnModel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

@Tag(name = "社会救助回调认证接口")
@RestController
@CrossOrigin
@RequestMapping(value = "/api/dify/social/assistance")
public interface SocialAssistanceApi {

    @Operation(summary = "社会救助认证")
    @PostMapping(value = "/authentication")
    ReturnModel authentication(@RequestBody JSONObject tokenBody);

}
