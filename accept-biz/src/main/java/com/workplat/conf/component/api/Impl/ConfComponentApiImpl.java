package com.workplat.conf.component.api.Impl;

import cn.hutool.core.bean.BeanUtil;
import com.workplat.conf.component.api.ConfComponentApi;
import com.workplat.conf.component.converter.PlatConfComponentConvert;
import com.workplat.conf.component.dto.ConfComponentDTO;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.conf.component.service.ConfComponentService;
import com.workplat.componentEngine.request.ComponentUsageRequest;
import com.workplat.componentEngine.vo.ComponentUsageSituationVO;
import com.workplat.conf.component.vo.ConfComponentVO;
import com.workplat.flow.service.ConfFlowComponentService;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

@RestController
public class ConfComponentApiImpl implements ConfComponentApi {

    @Resource
    private ConfComponentService confComponentService;
    @Resource
    private PlatConfComponentConvert convert;
    @Resource
    private ConfFlowComponentService confFlowComponentService;


    @Override
    @ApiLogging(module = "组件配置", operation = "新增组件", type = OperationType.INSERT)
    public ResponseData<ConfComponentVO> save(ConfComponentDTO confComponentDto) {
        ConfComponent confComponent = BeanUtil.copyProperties(confComponentDto, ConfComponent.class);
        ConfComponent component = confComponentService.save(confComponent);
        return ResponseData.success("保存成功", convert.convert(component));
    }

    @Override
    @ApiLogging(module = "组件配置", operation = "查询组件列表", type = OperationType.QUERY)
    public ResponseData<List<ConfComponentVO>> getConfComponentList(String keyword) {
        HashMap<String, Object> param = new HashMap<>();
        if (StringUtil.isNotBlank(keyword)) {
            param.put("like(name)", keyword);
        }
        List<ConfComponent> confComponents = confComponentService.queryForList(param);
        List<ConfComponentVO> confComponentVOS = convert.convert(confComponents);
        return ResponseData.success(confComponentVOS);
    }

    @Override
    public ResponseData<Page<ConfComponentVO>> getConfComponentPage(String keyword, PageableDTO pageDTO) {
        HashMap<String, Object> param = new HashMap<>();
        if (StringUtil.isNotBlank(keyword)) {
            param.put("like(name)", keyword);
        }
        Page<ConfComponent> confComponents = confComponentService.queryForPage(param, pageDTO.convertPageable());
        Page<ConfComponentVO> page = convert.convert(confComponents);
        return ResponseData.success(page);
    }

    @Override
    @ApiLogging(module = "组件配置", operation = "删除组件", type = OperationType.DELETE)
    public ResponseData<Void> delete(String id) {
        confComponentService.deleteById(id);
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "组件配置", operation = "查询组件", type = OperationType.QUERY)
    public ResponseData<ConfComponentVO> getByCode(String code) {
        ConfComponent confComponent = confComponentService.getByCode(code);
        if (confComponent != null) {
            return ResponseData.success(convert.convert(confComponent));
        }
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "组件配置", operation = "查询组件", type = OperationType.QUERY)
    public ResponseData<ConfComponentVO> getById(String id) {
        ConfComponent confComponent = confComponentService.queryById(id);
        if (confComponent != null) {
            return ResponseData.success(convert.convert(confComponent));
        }
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "组件配置", operation = "获取组件清单", type = OperationType.QUERY)
    public ResponseData<Page<ComponentUsageSituationVO>> getList(ComponentUsageRequest request) {
        Page<ComponentUsageSituationVO> page = confFlowComponentService.queryComponentUsage(request);
        return ResponseData.success(page);
    }
}
