//package com.workplat.gss.script.biz.loader;
package com.workplat.electronic.certificate;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.workplat.accept.business.chat.constant.NodeStatusEnum;
import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.service.BizInstanceChatService;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoDetailVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialFileVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialGroupVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.doc.pdf.util.PdfConvertUtils;
import com.workplat.gss.common.script.model.declare.DistributionDriverInput;
import com.workplat.gss.common.script.model.declare.DistributionDriverOutput;
import com.workplat.gss.common.script.service.declare.DistributionDriverLoader;
import com.workplat.gss.ext.dto.BizInstanceMaterialExtendJson;
import com.workplat.gss.file.service.SysFileEntityService;
import com.workplat.push.ga.service.GaDeclarePushService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 大型活动办件推送
 */
public class GaDistributionDriverLoaderImpl implements DistributionDriverLoader {

    Logger log = LoggerFactory.getLogger(GaDistributionDriverLoaderImpl.class);
    TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
    BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
    BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
    SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
    NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
    BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);


    @Override
    public DistributionDriverOutput distributionDriver(DistributionDriverInput input) {
        DistributionDriverOutput output = new DistributionDriverOutput();
        JSONObject oo = new JSONObject();

        // 保存一条会话办件记录

        BizInstanceChat chat = bizInstanceChatService.queryForSingle(ImmutableMap.of("=(instance.id)", input.getInstanceId()));
        try {
            if (chat != null) {
                pushV2(input.getInstanceId(), false);
            } else {
                pushV2(input.getInstanceId(), true);
                BizInstanceChat bizInstanceChat = new BizInstanceChat();
                bizInstanceChat.setInstance(bizInstanceInfoService.queryById(input.getInstanceId()));
                bizInstanceChat.setCurrentNode(NodeStatusEnum.NO_ACCEPT.name());
                bizInstanceChat.setCurrentNodeName(NodeStatusEnum.NO_ACCEPT.getValue());
                bizInstanceChatService.create(bizInstanceChat);
            }
        } catch (Exception e) {
            output.setSuccess(false);
            output.setMsg(e.getMessage());
        }
        return output;
    }


    public void pushV2(String instanceId, Boolean isFirstSubmit) {
        try {

            // 打印当前线程名
            gaTransactionTemplate.execute(status -> {
                // 1.推送基本办件信息和表单 信息
                BizInstanceInfoDetailVO detailVOById = bizInstanceInfoService.getDetailVOById(instanceId);
                BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
                List<BizInstanceMaterialGroupVO> materialGroup =
                        bizInstanceMaterialService.getMaterialGroup(instanceId);



                if (detailVOById != null) {
                    String sql = "INSERT INTO biz_ga_instance_info (id, create_time, accepted_num, application_address, application_certificate_code, " +
                                 "application_name, application_mobile, matter_code, matter_name, form_obj, fields_map, is_first_submit) " +
                                 "VALUES (:uuid, now(), :acceptedNum, :applicationAddress, :applicationCertificateCode, " +
                                 ":applicationName, :applicationMobile, :matterCode, :matterName, :formObj, :fieldsMap, :isFirstSubmit)";

                    // 参数
                    MapSqlParameterSource sqlParameterSource = new MapSqlParameterSource();
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    sqlParameterSource.addValue("uuid", uuid);
                    sqlParameterSource.addValue("acceptedNum", detailVOById.getAcceptedNum());
                    sqlParameterSource.addValue("applicationAddress", detailVOById.getApplicationAddress());
                    sqlParameterSource.addValue("applicationCertificateCode", detailVOById.getApplicationCertificateCode());
                    sqlParameterSource.addValue("applicationName", detailVOById.getApplicationName());
                    sqlParameterSource.addValue("applicationMobile", detailVOById.getApplicationPhone());
                    sqlParameterSource.addValue("matterCode", bizInstanceInfo.getMatterCode());
                    sqlParameterSource.addValue("matterName", bizInstanceInfo.getMatterName());
                    sqlParameterSource.addValue("formObj", coverFormObj(detailVOById.getBizInstanceFields().getFormObj()));
                    sqlParameterSource.addValue("fieldsMap", detailVOById.getBizInstanceFields().getFieldsMap());
                    sqlParameterSource.addValue("isFirstSubmit", isFirstSubmit ? 1 : 0);

                    int updateCount = gaJdbcTemplate.update(sql, sqlParameterSource);
                    if (updateCount > 0) {
                        // 2.推送材料信息
                        for (BizInstanceMaterialGroupVO materialGroupVO : materialGroup) {
                            for (BizInstanceMaterialVO materialVO : materialGroupVO.getInstanceMaterialVOList()) {
                                String materialSql = "INSERT INTO biz_ga_instance_material (id, create_time, material_id, material_code, material_description, material_name, instance_id)" +
                                                     "VALUES" +
                                                     "(:uuid, now(), :materialId, :materialCode, :materialDescription, :materialName, :instanceId)";

                                MapSqlParameterSource materialParams = new MapSqlParameterSource();
                                String materialUuid = UUID.randomUUID().toString().replace("-", "");
                                materialParams.addValue("uuid", materialUuid);
                                materialParams.addValue("materialId", materialVO.getId());
                                materialParams.addValue("materialCode", materialVO.getMaterialCode());
                                materialParams.addValue("materialDescription", materialGroupVO.getGroupName());
                                materialParams.addValue("materialName", materialVO.getMaterialName());
                                materialParams.addValue("instanceId", uuid);

                                int materialUpdateCount = gaJdbcTemplate.update(materialSql, materialParams);
                                if (materialUpdateCount > 0) {
                                    // 3.推送材料文件信息
                                    Map<String, byte[]> fileMap = pdfConvertPicture(materialVO); // 部分pdf转换为图片
                                    for (BizInstanceMaterialFileVO fileVO : materialVO.getMaterialFileVOList()) {
                                        String fileSql = "INSERT INTO biz_ga_instance_material_file (id, create_time, file_name, file_data, material_id)" +
                                                         "VALUES" +
                                                         "(:uuid, now(),  :fileName, :file_data, :materialId)";

                                        MapSqlParameterSource fileParams = new MapSqlParameterSource();
                                        String fileUuid = UUID.randomUUID().toString().replace("-", "");
                                        fileParams.addValue("uuid", fileUuid);
                                        fileParams.addValue("fileName", fileVO.getFileName());
                                        try {
                                            fileParams.addValue("file_data"
                                                    , fileMap.containsKey(fileVO.getFileId()) ?
                                                            fileMap.get(fileVO.getFileId()) : sysFileEntityService.binary(fileVO.getFileId()));
                                        } catch (Exception e) {
                                            throw new RuntimeException("获取文件数据失败", e);
                                        }
                                        fileParams.addValue("materialId", materialUuid);

                                        int fileUpdateCount = gaJdbcTemplate.update(fileSql, fileParams);
                                        if (fileUpdateCount <= 0) {
                                            throw new RuntimeException("材料文件信息插入失败，fileId: " + fileVO.getFileId());
                                        }
                                    }
                                } else {
                                    throw new RuntimeException("材料信息插入失败，materialId: " + materialVO.getId());
                                }
                            }
                        }
                    } else {
                        throw new RuntimeException("办件信息插入失败，instanceId: " + instanceId);
                    }
                }
                return null;
            });
        } catch (Exception e) {
            log.error("推送办件信息失败，触发事务回滚", e);
            // 重新抛出异常以触发事务回滚
            throw new BusinessException(StringUtils.substring(ExceptionUtils.getStackTrace(e), 0, 6000), e);
        }
    }

    private Object coverFormObj(String formObj) {
        if (StringUtils.isBlank(formObj)){
            return null;
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);

        // 创建新的封装结构
        Map<String, Object> newFormObjMap = new HashMap<>();
        
        // 按照指定结构封装数据
        JSONObject config = JSON.parseObject("""
                {
                  "活动详情": {
                    "sxlb": "事项类别",
                    "yjgmrs": "预计规模",
                    "hdmc": "活动名称",
                    "qsrq": "举办日期",
                    "jzsj": "结束日期",
                    "hdddlx": "举办地点",
                    "hdlx": "活动路线",
                    "hdmjlc": "场地面积（）",
                    "cdlc": "场地里程",
                    "edrl": "场地安全容量",
                    "nfsps": "拟发售票数",
                    "hdnr": "活动概况",
                    "sdzffzrxm": "属地政府负责人姓名",
                    "sdzffzrlxdh": "属地政府负责人联系电话",
                    "zgbmzrr": "主管部门责任人姓名",
                    "zgbmzrrlxdh": "主管部门责任人联系电话"
                  },
                  "主、承办方情况": {
                    "zbfmc": "主办方名称",
                    "zbffzrxm": "主办方负责人姓名",
                    "zbffzrlxdh": "主办方负责人联系电话",
                    "cbzmc": "承办方名称",
                    "cbzaqzrr": "承办方安全责任人姓名",
                    "cbzzw": "承办方安全责任人职务",
                    "cbzsfz": "承办方安全责任人身份证件类型",
                    "sfzjhm": "承办方安全责任人身份证件号码",
                    "aqzrrlxdh": "承办方安全责任人联系电话",
                    "lxrxm": "承办方联系人姓名",
                    "lxrzw": "承办方联系人职务",
                    "lxrdh": "承办方联系人联系电话"
                  },
                  "场地提供方情况": {
                    "glzmc": "场所管理者名称",
                    "glzyfzr": "主要负责人姓名",
                    "glfzrzw": "主要负责人职务",
                    "glzyfzrdh": "主要负责人联系电话",
                    "gllxr": "联系人姓名",
                    "glzw": "联系人职务",
                    "gldh": "联系人联系电话"
                  },
                  "临时搭建情况": {
                    "dwmc": "单位名称",
                    "qylx": "企业类型",
                    "qyzch": "企业注册号",
                    "qyfzr": "主要负责人姓名",
                    "qyfzrzw": "主要负责人职务",
                    "qyfzrlxdh": "主要负责人联系电话"
                  },
                  "保安提供方情况": {
                    "bagsmc": "保安公司名称",
                    "bagsfzr": "主要负责人姓名",
                    "bagsfzrzw": "主要负责人职务",
                    "bagsfzrdh": "主要负责人联系电话",
                    "bagssgsl": "受雇保安员数量",
                    "bagsbz": "备注"
                  },
                  "证件管理方案": {
                    "gzzjlb": "工作证件类别",
                    "gzzjsl": "工作证件数量"
                  },
                  "票务管理方案": {
                    "pwsl": "票务数量",
                    "smpt": "售卖平台",
                    "smjh": "售卖计划",
                    "pwgsmc": "票务公司名称",
                    "pwgsfzrlxdh": "票务公司负责人联系电话"
                  }
                }""");
        
        // 遍历配置，按分类封装数据
        for (Map.Entry<String, Object> entry : config.entrySet()) {
            String categoryName = entry.getKey();
            JSONObject categoryFields = (JSONObject) entry.getValue();
            
            Map<String, Object> categoryData = new HashMap<>();
            for (Map.Entry<String, Object> fieldEntry : categoryFields.entrySet()) {
                String fieldKey = fieldEntry.getKey();
                Object fieldValue = formObjMap.get(fieldKey);
                if (fieldValue != null) {
                    categoryData.put(fieldKey, fieldValue);
                }
            }
            
            if (!categoryData.isEmpty()) {
                newFormObjMap.put(categoryName, categoryData);
            }
        }
        
        return JSONObject.toJSONString(newFormObjMap);
    }

    /**
     * pdf转换为图片
     *
     * @param materialVO
     */
    private Map<String, byte[]> pdfConvertPicture(BizInstanceMaterialVO materialVO) {
        // 材料配置为纸质的时候才转换成图片
        if (!"PAPER".equals(materialVO.getSubmitFormat())) {
            return MapUtil.newHashMap();
        }
        Map<String, byte[]> fileMap = new HashMap<>();
        List<BizInstanceMaterialFileVO> materialFileVOList = materialVO.getMaterialFileVOList();
        int index = 0;
        for (BizInstanceMaterialFileVO fileVO : materialFileVOList) {
            byte[] binary = null;
            try {
                binary = sysFileEntityService.binary(fileVO.getFileId());
            } catch (Exception e) {
                throw new RuntimeException("获取文件数据失败", e);
            }
            // 转换为图片,目前业务只会有一张图片
            List<byte[]> image = null;
            try {
                image = PdfConvertUtils.pdf2Image(binary, 50);
            } catch (Exception e) {
                continue;
            }
            if (image.isEmpty()) {
                continue;
            }
            fileMap.put(fileVO.getFileId(), image.get(0));

            // 文件后缀变成.png
            fileVO.setFileName(fileVO.getFileName().replace(".pdf", ".jpg"));
        }
        return fileMap;
    }
}