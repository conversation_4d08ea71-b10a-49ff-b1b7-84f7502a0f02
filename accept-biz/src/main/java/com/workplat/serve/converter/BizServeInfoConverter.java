package com.workplat.serve.converter;

import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.accept.business.serve.vo.BizServeInfoVo;
import com.workplat.accept.business.serve.vo.BizServeMethodVo;
import com.workplat.gss.common.core.converter.BaseConverter;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;


@Component
public class BizServeInfoConverter implements BaseConverter<BizServeInfo, BizServeInfoVo> {

    @Autowired
    private BizServeMethodConverter bizServeMethodConverter;

    @Override
    public BizServeInfoVo convert(BizServeInfo source) {
        BizServeInfoVo bizServeInfoVo = new BizServeInfoVo();
        if (!source.getMethodList().isEmpty()) {
            bizServeInfoVo.setMethodList(bizServeMethodConverter.convert(source.getMethodList()));
        }
        return getBizServeInfoVo(source, bizServeInfoVo);
    }


    public BizServeInfoVo convert(BizServeInfo source,String channel) {
        BizServeInfoVo bizServeInfoVo = new BizServeInfoVo();
        if (!source.getMethodList().isEmpty()) {
            List<BizServeMethodVo> convert = bizServeMethodConverter.convert(source.getMethodList());
            // 如果是微信渠道则过滤掉PC渠道,如果是PC渠道则过滤掉微信渠道

            String deleteChannel = "WX".equals(channel) ? "PC" : "WX";
            convert = convert.stream().filter(member -> !member.getType().equals(deleteChannel)).toList();
            bizServeInfoVo.setMethodList(convert);
        }
        return getBizServeInfoVo(source, bizServeInfoVo);
    }

    @NotNull
    private BizServeInfoVo getBizServeInfoVo(BizServeInfo source, BizServeInfoVo bizServeInfoVo) {
        bizServeInfoVo.setId(source.getId());
        bizServeInfoVo.setName(source.getName());
        bizServeInfoVo.setType(source.getType());
        bizServeInfoVo.setGuideType(source.getGuideType());
        bizServeInfoVo.setGuideUrl(source.getGuideUrl());
        bizServeInfoVo.setEnable(source.isEnable());
        bizServeInfoVo.setDescription(source.getDescription());
        bizServeInfoVo.setCode(source.getCode());
        bizServeInfoVo.setThirdParty(source.isThirdParty());
        return bizServeInfoVo;
    }

}
