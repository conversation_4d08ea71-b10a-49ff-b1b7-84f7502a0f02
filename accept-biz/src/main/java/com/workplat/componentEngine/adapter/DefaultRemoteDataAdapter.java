package com.workplat.componentEngine.adapter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.workplat.componentEngine.engine.AbstractInstructionRemoteEngine;
import com.workplat.componentEngine.engine.InstructionRemoteEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;

/**
 * 默认远程数据适配器实现
 * 处理标准的远程数据获取和转换逻辑
 * 
 * <AUTHOR>
 * @date 2025/06/25
 */
@Slf4j
@Component
public class DefaultRemoteDataAdapter implements RemoteDataAdapter {
    
    private final RestTemplate restTemplate;
    
    public DefaultRemoteDataAdapter(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }
    
    @Override
    public String fetchData(String url, HttpMethod method, HttpHeaders headers, String requestBody, Map<String, Object> params) {
        try {
            // 处理GET请求参数拼接
            if (method == HttpMethod.GET && params != null && !params.isEmpty()) {
                UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(url);
                params.forEach((key, value) -> {
                    if (value != null) {
                        uriBuilder.queryParam(key, value.toString());
                    }
                });
                url = uriBuilder.toUriString();
            }

            // 如果是POST/PUT/PATCH等带有请求体的方法，且请求体不为空，设置Content-Type为application/json
            if ((method == HttpMethod.POST || method == HttpMethod.PUT || method == HttpMethod.PATCH)
                && requestBody != null && !requestBody.isEmpty()) {
                if (!headers.containsKey(HttpHeaders.CONTENT_TYPE)) {
                    headers.setContentType(MediaType.APPLICATION_JSON);
                }
            }

            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, method, requestEntity, String.class);
            JSONObject jsonObject = JSONObject.parseObject(response.getBody());
            if (jsonObject != null) {
                if (jsonObject.get("data") != null) {
                    return jsonObject.get("data").toString();
                }
                return jsonObject.toString();
            }
        } catch (Exception e) {
            log.error("远程数据获取失败: url={}, method={}, error={}", url, method, e.getMessage());
            return null;
        }
        return null;
    }
    
    @Override
    public Object transformData(String responseBody) {
        if (responseBody == null) {
            return Collections.emptyList();
        }
        
        try {
            // 解析为List<List<?>>格式
            return JSON.<List<List<?>>>parseObject(responseBody, new TypeReference<>() {});
        } catch (Exception e) {
            log.error("数据转换失败: responseBody={}, error={}", responseBody, e.getMessage());
            return Collections.emptyList();
        }
    }
    
    @Override
    public JSONObject getConfig(String content, String configKey) {
        try {
            JSONObject input = JSON.parseObject(content);
            
            // 支持嵌套配置键，如"buttons.detail"
            String[] keys = configKey.split("\\.");
            JSONObject current = input;
            
            for (String key : keys) {
                if (current == null) {
                    return null;
                }
                current = current.getJSONObject(key);
            }
            
            return current;
        } catch (Exception e) {
            log.error("配置解析失败: content={}, configKey={}, error={}", content, configKey, e.getMessage());
            return null;
        }
    }
}
