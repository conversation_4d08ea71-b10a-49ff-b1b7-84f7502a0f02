package com.workplat.componentEngine.engine;

import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.adapter.InstructionDataTransformer;
import com.workplat.componentEngine.adapter.RemoteDataAdapter;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.matter.service.ConfMatterExtendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> cheng
 * @package com.workplat.componentEngine.engine
 * @description 申报须知（第三方接口获取）组件引擎
 * @date 2025/5/28 14:41
 */
@Slf4j
@Service
@Order(90) // 默认申报须知组件 - 较低优先级，确保特定业务的子类组件优先匹配
public class InstructionRemoteEngine extends AbstractInstructionRemoteEngine {

    public InstructionRemoteEngine(@Qualifier("defaultRemoteDataAdapter") RemoteDataAdapter remoteDataAdapter,
                                   InstructionDataTransformer dataTransformer,
                                   BizInstanceFieldsService bizInstanceFieldsService,
                                   BizInstanceInfoService bizInstanceInfoService,
                                   ConfMatterExtendService confMatterExtendService) {
        super(remoteDataAdapter, dataTransformer, bizInstanceFieldsService, bizInstanceInfoService, confMatterExtendService);
    }

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        log.info("InstructionRemoteEngine execute");
       return process(componentDataContext);
    }
}

