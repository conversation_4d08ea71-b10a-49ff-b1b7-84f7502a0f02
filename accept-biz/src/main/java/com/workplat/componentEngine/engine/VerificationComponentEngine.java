package com.workplat.componentEngine.engine;

import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialGroupVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.push.entity.InstancePushLog;
import com.workplat.push.service.InstancePushLogService;
import com.workplat.utils.ChatCacheUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @description: 信息核验组件
 * @date 2025/06/04
 */
@Slf4j
@Service
public class VerificationComponentEngine extends AbstractComponentEngine {


    private final BizInstanceInfoService bizInstanceInfoService;
    private final InstancePushLogService instancePushLogService;
    private final BizInstanceMaterialService bizInstanceMaterialService;

    String CODE =  ComponentEngineCode.VERIFICATION;

    public VerificationComponentEngine(BizInstanceInfoService bizInstanceInfoService,
                                       InstancePushLogService instancePushLogService,
                                       BizInstanceMaterialService bizInstanceMaterialService) {
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.instancePushLogService = instancePushLogService;
        this.bizInstanceMaterialService = bizInstanceMaterialService;
    }

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        // 获取实例信息
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());

        List<BizInstanceMaterialGroupVO> materialGroup =
                bizInstanceMaterialService.getMaterialGroup(componentDataContext.getInstanceId());

        VerificationData verificationData = VerificationData.builder()
                .instanceId(componentDataContext.getInstanceId())
                .matterName(bizInstanceInfo.getMatterName())
                .hasMaterialGroup(!materialGroup.isEmpty())
                .build();

        log.info("信息核验组件执行");
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(verificationData)
                .build();

        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(renderData);
        vo.setRenderData(renderDataList);
        vo.setTips(getPropertyValue(componentDataContext, "tips"));
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Builder
    @Data
    static class VerificationData {
        private String instanceId;
        private String matterName;
        private Boolean hasMaterialGroup;
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 申报结束提交
        bizInstanceInfoService.endSubmit(componentDataContext.getInstanceId(), null);
        // 保存推送记录
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());
        InstancePushLog pushLog = instancePushLogService.queryForSingle(Map.of("=(instance.id)", componentDataContext.getInstanceId()));
        if (pushLog == null) {
            InstancePushLog instancePushLog = new InstancePushLog();
            instancePushLog.setInstance(bizInstanceInfo);
            instancePushLog.setPushStatus("0");
            instancePushLogService.save(instancePushLog);
        }
    }


}
