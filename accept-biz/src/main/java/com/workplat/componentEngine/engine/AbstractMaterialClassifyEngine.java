package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.dto.FileClassifyDTO;
import com.workplat.accept.business.chat.dto.FileDTO;
import com.workplat.accept.business.chat.service.MaterialClassificationService;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.biz.converter.BizInstanceFieldsConvert;
import com.workplat.gss.application.dubbo.dto.BizInstanceMaterialSubmitDTO;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.utils.ChatCacheUtil;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 材料分类组件引擎基类
 * 提供材料分类的通用功能，包括：
 * 1. 材料分类处理
 * 2. 组件处理能力判断
 * 3. 材料提交DTO转换
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
@Order(100) // 抽象材料分类组件 - 最低优先级，确保具体业务的子类组件优先匹配
public abstract class AbstractMaterialClassifyEngine extends AbstractComponentEngine {

    /**
     * 材料分类服务
     */
    protected final MaterialClassificationService materialClassificationService;

    /**
     * 聊天缓存工具
     */
    protected final ChatCacheUtil chatCacheUtil;

    /**
     * 实例字段服务
     */
    protected final BizInstanceFieldsService bizInstanceFieldsService;

    /**
     * 业务实例字段转换器
     */
    private final BizInstanceFieldsConvert bizInstanceFieldsConvert;

    /**
     * 业务实例材料服务
     */
    private final BizInstanceMaterialService bizInstanceMaterialService;

    /**
     * 材料分类key
     */
    @Value("${materialsClassifyKey:}")
    protected String materialsClassifyKey;

    /**
     * 字段识别key
     */
    @Value("${fileRecognitionKey:app-bGVVIyj8ZoTiSuLjVnp9wYxJ}")
    protected String fileRecognitionKey;

    /**
     * 组件代码
     */
    protected final String CODE = ComponentEngineCode.MATERIAL_UPLOAD;

    /**
     * 构造函数
     *
     * @param materialClassificationService 材料分类服务
     * @param chatCacheUtil                 聊天缓存工具
     * @param bizInstanceFieldsService      实例字段服务
     */
    protected AbstractMaterialClassifyEngine(MaterialClassificationService materialClassificationService,
                                             ChatCacheUtil chatCacheUtil,
                                             BizInstanceFieldsService bizInstanceFieldsService,
                                             BizInstanceMaterialService bizInstanceMaterialService,
                                             BizInstanceFieldsConvert bizInstanceFieldsConvert) {
        this.materialClassificationService = materialClassificationService;
        this.chatCacheUtil = chatCacheUtil;
        this.bizInstanceFieldsService = bizInstanceFieldsService;
        this.bizInstanceMaterialService = bizInstanceMaterialService;
        this.bizInstanceFieldsConvert = bizInstanceFieldsConvert;
    }

    /**
     * 处理材料分类
     * 主要流程：
     * 1. 构建文件分类DTO
     * 2. 获取表单信息
     * 3. 调用材料分类服务处理
     * 4. 更新缓存
     * 5. 组装渲染数据
     *
     * @param bizInstanceMaterialVOS 业务实例材料列表
     * @param chatProcessDTO         聊天处理DTO
     * @param componentDataContext
     * @return 组件运行结果
     */
    protected ComponentRunVO processMaterialClassification(List<BizInstanceMaterialVO> bizInstanceMaterialVOS,
                                                           ChatProcessDTO chatProcessDTO, ComponentDataContext componentDataContext) {
        ComponentRunVO vo = new ComponentRunVO();
        if (chatProcessDTO.getFileUrls() != null && !chatProcessDTO.getFileUrls().isEmpty()) {
            // 获取文件分类结果-调用文件分类服务
            FileClassifyDTO dto = FileClassifyDTO.builder()
                    .userId(componentDataContext.getInstanceId())
                    .fileUrls(chatProcessDTO.getFileUrls().stream()
                            .map(FileDTO::new)  // 使用拷贝构造方法进行深拷贝
                            .collect(Collectors.toList()))
                    .build();
            // 获取表单信息
            BizInstanceFields bizInstanceFields =
                    bizInstanceFieldsService.queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
            BizInstanceFieldsVO bizInstanceFieldsVO = new BizInstanceFieldsVO();
            bizInstanceFieldsConvert.convert(bizInstanceFields, bizInstanceFieldsVO);

            // 处理材料分类
            MaterialClassificationService.MaterialClassificationResult result =
                    materialClassificationService.processClassification(
                            dto,
                            bizInstanceMaterialVOS,
                            bizInstanceFieldsVO,
                            materialsClassifyKey);

            // 存储AI提取的字段到缓存
            updateAiExtractFields(chatProcessDTO, result, componentDataContext);
            // 更新formObj
            updateFormObj(bizInstanceFields, result, componentDataContext);
            // 组装渲染数据
            ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                    .componentName(CODE)
                    .componentInfo(result.getMaterialVOS())
                    .build();
            vo.setRenderData(List.of(renderData));
        } else {
            // 组装渲染数据
            ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                    .componentName(CODE)
                    .componentInfo(bizInstanceMaterialVOS)
                    .build();
            vo.setRenderData(List.of(renderData));
        }
        return vo;
    }

    private void updateFormObj(BizInstanceFields bizInstanceFields, MaterialClassificationService.MaterialClassificationResult result, ComponentDataContext componentDataContext) {
        Map<String, Object> extractedFields = result.getExtractedFields();
        if (extractedFields != null) {
            String formObj = bizInstanceFields.getFormObj();
            if (formObj != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
                formObjMap.putAll(extractedFields);
                bizInstanceFields.setFormObj(JSON.toJSONString(formObjMap));
            } else {
                bizInstanceFields.setFormObj(JSON.toJSONString(extractedFields));
            }
            bizInstanceFieldsService.update(bizInstanceFields);
        }
    }

    private void updateAiExtractFields(ChatProcessDTO chatProcessDTO, MaterialClassificationService.MaterialClassificationResult result, ComponentDataContext componentDataContext) {
        Map<String, Object> aiExtractFields = chatProcessDTO.getAiExtractFields();
        if (aiExtractFields == null) {
            aiExtractFields = new HashMap<>();
        }
        aiExtractFields.putAll(result.getExtractedFields());
        chatProcessDTO.setAiExtractFields(aiExtractFields);
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
    }

    /**
     * 转换业务实例材料提交DTO列表
     * 将业务实例材料VO列表转换为提交DTO列表
     *
     * @param bizInstanceMaterialVOS 业务实例材料VO列表
     * @param instanceId             实例ID
     * @return 业务实例材料提交DTO列表
     */
    protected List<BizInstanceMaterialSubmitDTO> convertInstanceMaterialSubmitDTOS(List<BizInstanceMaterialVO> bizInstanceMaterialVOS, String instanceId) {
        return bizInstanceMaterialVOS.stream()
                .map(bizInstanceMaterialVO -> {
                            BizInstanceMaterialSubmitDTO bizInstanceMaterialSubmitDTO = new BizInstanceMaterialSubmitDTO();
                            BeanUtils.copyProperties(bizInstanceMaterialVO, bizInstanceMaterialSubmitDTO);
                            bizInstanceMaterialSubmitDTO.setInstanceId(instanceId);
                            bizInstanceMaterialSubmitDTO.setMaterialFileList(bizInstanceMaterialVO.getMaterialFileVOList().stream()
                                    .map(bizInstanceMaterialFileVO -> {
                                        BizInstanceMaterialSubmitDTO.MaterialFile materialFile = new BizInstanceMaterialSubmitDTO.MaterialFile();
                                        BeanUtils.copyProperties(bizInstanceMaterialFileVO, materialFile);
                                        return materialFile;
                                    }).toList());
                            return bizInstanceMaterialSubmitDTO;
                        }
                ).filter(bizInstanceMaterialVO -> !bizInstanceMaterialVO.getId().isEmpty()).toList();
    }

    /**
     * 判断是否可以处理给定的组件数据上下文
     *
     * @param context 组件数据上下文
     * @return 如果组件代码匹配则返回true，否则返回false
     */
    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 获取提交数据
        Object submitData = componentDataContext.getSubmitData();

        Collection<ComponentRunVO.RenderData> renderDataList =
                JSONArray.parseArray(submitData.toString(), ComponentRunVO.RenderData.class);
        ComponentRunVO.RenderData renderData = renderDataList.iterator().next();

        List<BizInstanceMaterialVO> bizInstanceMaterialVOS = JSONArray.parseArray(renderData.getComponentInfo().toString(), BizInstanceMaterialVO.class);
        // 转换成List<BizInstanceMaterialSubmitDTO>
        List<BizInstanceMaterialSubmitDTO> bizInstanceMaterialSubmitDTOS =
                convertInstanceMaterialSubmitDTOS(bizInstanceMaterialVOS, componentDataContext.getInstanceId());
        // 调用批量保存接口
        bizInstanceMaterialService.tempSubmit(bizInstanceMaterialSubmitDTOS);
    }

    protected Map<String, Object> processMaterialRecognize(List<BizInstanceMaterialVO> instanceMaterialVOList, ComponentDataContext componentDataContext) {
        // 获取表单信息
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId());

        MaterialClassificationService.MaterialClassificationResult result = materialClassificationService.processRecognition(
                instanceMaterialVOList,
                bizInstanceFieldsVO,
                fileRecognitionKey);

        return result.getExtractedFields();
    }
}