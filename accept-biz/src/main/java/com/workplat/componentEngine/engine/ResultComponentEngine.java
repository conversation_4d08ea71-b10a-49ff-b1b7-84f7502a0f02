package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.accept.business.chat.vo.CustomNameListVO;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.dto.ComponentPropsDTO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.constant.BizInstanceExtendJsonEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.ext.dto.BizInstanceMaterialExtendJson;
import com.workplat.matter.entity.BizNetworkWindow;
import com.workplat.matter.service.BizNetworkWindowService;
import com.workplat.matter.service.ConfMatterExtendService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @description: 结果组件引擎
 * @date 2025/06/04
 */
@Slf4j
@Service
public class ResultComponentEngine extends AbstractComponentEngine {

    private final BizInstanceMaterialService bizInstanceMaterialService;
    private final ConfMatterExtendService confMatterExtendService;
    private final BizInstanceInfoService bizInstanceInfoService;
    private final BizNetworkWindowService networkWindowService;

    public ResultComponentEngine(BizInstanceMaterialService bizInstanceMaterialService,
                                 ConfMatterExtendService confMatterExtendService,
                                 BizInstanceInfoService bizInstanceInfoService, BizNetworkWindowService networkWindowService) {
        this.bizInstanceMaterialService = bizInstanceMaterialService;
        this.confMatterExtendService = confMatterExtendService;
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.networkWindowService = networkWindowService;
    }

    private final String CODE = ComponentEngineCode.RESULT;


    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        List<ComponentRunVO.RenderData> renderDataList = Lists.newArrayList();

        // 办理地址坐标
        ResultData.Coordinate coordinate = null;
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());
        JSONObject extendJson = JSON.parseObject(bizInstanceInfo.getExtendJson());
        if (extendJson != null) {
            String netWorkWindowCode = (String) BizInstanceExtendJsonEnum.NET_WORK_WINDOW.convertObject(bizInstanceInfo);
            if (StringUtils.isNotBlank(netWorkWindowCode)) {
                coordinate = new ResultData.Coordinate();
                BizNetworkWindow networkWindow = networkWindowService.queryForSingle(MapUtil.<String, Object>builder().put("=(code)", netWorkWindowCode).build());
                coordinate.setLatitude(networkWindow.getLatitude());
                coordinate.setLongitude(networkWindow.getLongitude());
                coordinate.setNetworkName(networkWindow.getNetworkName());
                coordinate.setWindowInfo(networkWindow.getWindowInfo());
            }
        }

        // 获取提交提示语
        String informAfterSubmit = getPropertyValue(componentDataContext, "submitText", "提交成功");
        // 替换告知信息中的占位符
        if (coordinate != null) {
            informAfterSubmit = informAfterSubmit
                    .replace("#network#", coordinate.getNetworkName())
                    .replace("#window#", coordinate.getWindowInfo());
        }
        // 1. 首先添加 submitTips
        ComponentRunVO.RenderData submitTips = ComponentRunVO.RenderData.builder()
                .componentName("submitTips")
                .componentInfo(informAfterSubmit)
                .build();
        renderDataList.add(submitTips);

        // 2. 然后添加 RelatedMaterials
        List<BizInstanceMaterialVO> bizInstanceMaterialVOS = new ArrayList<>();
        List<BizInstanceMaterialVO> instanceMaterialVOS =
                bizInstanceMaterialService.getInstanceMaterial(componentDataContext.getInstanceId());
        for (BizInstanceMaterialVO bizInstanceMaterialVO : instanceMaterialVOS) {
            String extendJson1 = bizInstanceMaterialVO.getExtendJson();
            if (StringUtils.isNotBlank(extendJson1)) {
                BizInstanceMaterialExtendJson materialExtendJson = BizInstanceMaterialExtendJson.convert(extendJson1);
                if (materialExtendJson.getIsFinalSubmitShow() != null && materialExtendJson.getIsFinalSubmitShow()) {
                    bizInstanceMaterialVOS.add(bizInstanceMaterialVO);
                }
            }
        }
        if (!bizInstanceMaterialVOS.isEmpty()) {
            ComponentRunVO.RenderData relatedMaterials = ComponentRunVO.RenderData.builder()
                    .componentName("RelatedMaterials")
                    .componentCnName("关联材料")
                    .componentInfo(bizInstanceMaterialVOS)
                    .build();
            renderDataList.add(relatedMaterials);
        }

        // 3. 然后添加 LocationApplication
        if (coordinate != null) {
            ComponentRunVO.RenderData locationApplication = ComponentRunVO.RenderData.builder()
                    .componentName("LocationApplication")
                    .componentCnName("线下地点办理")
                    .componentInfo(coordinate)
                    .build();
            renderDataList.add(locationApplication);
        }

        // 4. 最后添加 CustomNameList
        if (extendJson.containsKey(BizInstanceExtendJsonEnum.CUSTOM_NAME_LIST.getCode())) {
            CustomNameListVO customNameListVO = (CustomNameListVO) BizInstanceExtendJsonEnum.CUSTOM_NAME_LIST.convertObject(bizInstanceInfo);
            ComponentRunVO.RenderData locationApplication = ComponentRunVO.RenderData.builder()
                    .componentName("CustomNameList")
                    .componentCnName(customNameListVO.getName())
                    .componentInfo(customNameListVO.getList())
                    .build();
            renderDataList.add(locationApplication);
        }

        // 组件运行VO
        ComponentRunVO vo = new ComponentRunVO();
        vo.setRenderData(renderDataList);

        // 返回结果
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Data
    static class ResultData {

        /**
         * 坐标类
         */
        @Data
        static class Coordinate {

            /**
             * 经度
             */
            private String longitude;
            /**
             * 纬度
             */
            private String latitude;
            /**
             * 网点名称
             */
            private String networkName;
            /**
             * 窗口信息
             */
            private String windowInfo;
        }
    }
}
