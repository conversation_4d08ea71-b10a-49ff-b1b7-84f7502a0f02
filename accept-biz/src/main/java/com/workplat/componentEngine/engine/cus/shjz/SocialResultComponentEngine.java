package com.workplat.componentEngine.engine.cus.shjz;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.constant.NodeStatusEnum;
import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.service.BizInstanceChatService;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.engine.AbstractComponentEngine;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.extend.socialAssistance.dto.RescueUserInfoDto;
import com.workplat.extend.socialAssistance.service.JYRescuePlatformClient;
import com.workplat.extend.socialAssistance.vo.RescueUserInfoVO;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 社会救助信息提交组件
 *
 * <AUTHOR>
 * @description: 结果组件引擎
 * @date 2025/07/22
 */
@Slf4j
@Service
public class SocialResultComponentEngine extends AbstractComponentEngine {

    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;
    @Autowired
    private BizInstanceFieldsService bizInstanceFieldsService;
    @Autowired
    private JYRescuePlatformClient jyRescuePlatformClient;
    @Autowired
    private BizInstanceChatService bizInstanceChatService;


    private final String CODE = ComponentEngineCode.SOCIAL_RESULT;

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        List<ComponentRunVO.RenderData> renderDataList = Lists.newArrayList();

        String instanceId = componentDataContext.getInstanceId();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(instanceId);

        // 提交诉求申请
        JSONObject jsonObject = JSONObject.parseObject(bizInstanceFieldsVO.getFormObj());
        JSONArray jtcyListYdJsonArray = jsonObject.getJSONArray("jtcyListYd") == null ?
                new JSONArray() : jsonObject.getJSONArray("jtcyListYd");
        RescueUserInfoDto rescueUserInfoDto = new RescueUserInfoDto();
        List<RescueUserInfoDto.FamilyMember> jtcyListYd = null;
        BeanUtil.copyProperties(jsonObject, rescueUserInfoDto);
        jtcyListYd = BeanUtil.copyToList(jtcyListYdJsonArray, RescueUserInfoDto.FamilyMember.class);

        rescueUserInfoDto.setJtcyListYd(jtcyListYd);
        convertData(rescueUserInfoDto, jsonObject);

        // 提交诉求申请
        // 测试环境下aNetToken也需要拿到江阴正式环境token
//        componentDataContext.getInputs().put("aNetToken", "1fcc317b-40f5-457f-bdb3-45106f516e15");
//        List<RescueUserInfoVO> rescueUserInfoVOList = jyRescuePlatformClient.submitAppeal(rescueUserInfoDto, componentDataContext.getInputs().get("aNetToken").toString());
        List<RescueUserInfoVO> rescueUserInfoVOList = JSONArray.parseArray("""
                [{
                  "bksx": [
                    {
                      "xm": "张三",
                      "sfz": "110101199003076578",
                      "pname": "民政局",
                      "name": "住房救助",
                      "bxsyy": "收入超过当地平均水平"
                    },
                    {
                      "xm": "李四",
                      "sfz": "110101198505097890",
                      "pname": "人社局",
                      "name": "医疗救助",
                      "bxsyy": "已有医保且符合标准"
                    }
                  ],
                  "nkxs": [
                    {
                      "xm": "王五",
                      "sfz": "110101199208126598",
                      "pname": "教育局",
                      "name": "教育救助",
                      "nkxstsy": "符合条件，可申请教育补助"
                    },
                    {
                      "xm": "赵六",
                      "sfz": "110101198811158976",
                      "pname": "人社局",
                      "name": "就业救助",
                      "nkxstsy": "可提供就业培训和岗位推荐"
                    }
                  ]
                }]
                """, RescueUserInfoVO.class);

        ComponentRunVO.RenderData locationApplication = ComponentRunVO.RenderData.builder()
                .componentName("RescueUserInfo")
                .componentCnName("下面是匹配到社会救助事项清单，请等待相关部门进一步核实。")
                .componentInfo(rescueUserInfoVOList)
                .build();
        renderDataList.add(locationApplication);

        // 组件运行VO
        ComponentRunVO vo = new ComponentRunVO();
        vo.setRenderData(renderDataList);
        vo.setEngineCode(CODE);

        // 保存BizInstanceChat
//        BizInstanceChat bizInstanceChat = new BizInstanceChat();
//        bizInstanceChat.setInstance(bizInstanceInfo);
//        bizInstanceChat.setCurrentNode(NodeStatusEnum.BUSINESS_ACCEPTING.name());
//        bizInstanceChat.setCurrentNodeName(NodeStatusEnum.BUSINESS_ACCEPTING.getValue());
//        bizInstanceChatService.create(bizInstanceChat);


        // 返回结果
        return vo;
    }

    /**
     * 转换数据
     *
     * @param rescueUserInfoDto
     * @param jsonObject
     */
    private void convertData(RescueUserInfoDto rescueUserInfoDto, JSONObject jsonObject) {
        // zbhhjblx拿到是类型拼接字符串 如果有值就转换为1 否则0
        rescueUserInfoDto.setSfhzbhhjb(StringUtils.isNotEmpty(jsonObject.getString("zbhhjblx")) ? "1" : "0");
        // 江阴市,申港街道,320281009201，截取后面地区编码
        String[] split = jsonObject.getString("xjzd").split("[/,]");
        rescueUserInfoDto.setXjzd(split[split.length - 1]);
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

}
