package com.workplat.componentEngine.engine.cus.shjz;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.accept.business.chat.vo.CustomNameListVO;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.engine.SituationComponentEngine;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.api.api.BizInstanceSituationApi;
import com.workplat.gss.application.dubbo.constant.BizInstanceExtendJsonEnum;
import com.workplat.gss.application.dubbo.constant.InstanceApplicationStatusEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.application.dubbo.vo.ConfMatterAcceptQuotaVO;
import com.workplat.gss.common.core.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 社会救助情形选择组件引擎
 * 这个类暂时没有业务用到
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Deprecated
@Slf4j
@Service
public class SocialSituationComponentEngine extends SituationComponentEngine {

    private final BizInstanceSituationApi bizInstanceSituationApi;
    private final BizInstanceMaterialService bizInstanceMaterialService;
    private final BizInstanceInfoService bizInstanceInfoService;

    private final String CODE = ComponentEngineCode.SOCIAL_SITUATION;
    private final String situation = ComponentEngineCode.SITUATION;

    protected SocialSituationComponentEngine(BizInstanceSituationApi bizInstanceSituationApi,
                                             BizInstanceMaterialService bizInstanceMaterialService,
                                             BizInstanceInfoService bizInstanceInfoService) {
        super(bizInstanceSituationApi);
        this.bizInstanceSituationApi = bizInstanceSituationApi;
        this.bizInstanceMaterialService = bizInstanceMaterialService;
        this.bizInstanceInfoService = bizInstanceInfoService;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        try {
            Thread.sleep(6000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        //  调用API获取数据
        ResponseData<List<ConfMatterAcceptQuotaVO>> listResponseData =
                bizInstanceSituationApi.getByMatterId("6c2845440f2d42e88e280146077fae1f");
        List<ConfMatterAcceptQuotaVO> data = listResponseData.getData();

        // 封装返回数据
        ComponentRunVO componentRunVO = new ComponentRunVO();
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(situation)
                .componentInfo(data)
                .build();
        //  设置返回数据
        componentRunVO.setRenderData(List.of(renderData));
        componentRunVO.setEngineCode(situation);
        return componentRunVO;
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        List<String> list = new ArrayList<>();
        // 设置最终展示事项列表
        List<BizInstanceMaterialVO> instanceMaterialVOS =
                bizInstanceMaterialService.getInstanceMaterial(componentDataContext.getInstanceId());
        for (BizInstanceMaterialVO bizInstanceMaterialVO : instanceMaterialVOS) {
            list.add(bizInstanceMaterialVO.getMaterialName());
        }
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());
        CustomNameListVO customNameListVO = new CustomNameListVO();
        customNameListVO.setName("事项列表");
        customNameListVO.setList(list);
        bizInstanceInfo.extendJsonAdd(JSONObject.of(BizInstanceExtendJsonEnum.CUSTOM_NAME_LIST.getCode(), customNameListVO));
        bizInstanceInfo.setApplicationStatusEnum(InstanceApplicationStatusEnum.PENDING);
        bizInstanceInfoService.update(bizInstanceInfo);
    }
}
