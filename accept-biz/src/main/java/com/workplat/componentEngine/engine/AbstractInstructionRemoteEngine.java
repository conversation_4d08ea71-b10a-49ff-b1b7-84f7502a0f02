package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.adapter.InstructionDataTransformer;
import com.workplat.componentEngine.adapter.RemoteDataAdapter;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.dto.ComponentPropsDTO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.matter.service.ConfMatterExtendService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.*;

import java.util.*;

@Order(100) // 抽象申报须知组件 - 最低优先级，确保具体业务的子类组件优先匹配
public abstract class AbstractInstructionRemoteEngine extends AbstractComponentEngine {

    private final String CODE = ComponentEngineCode.INSTRUCTION_REMOTE;
    private final RemoteDataAdapter remoteDataAdapter;
    private final InstructionDataTransformer dataTransformer;
    private final BizInstanceFieldsService bizInstanceFieldsService;
    private final BizInstanceInfoService bizInstanceInfoService;
    private final ConfMatterExtendService confMatterExtendService;


    public AbstractInstructionRemoteEngine(RemoteDataAdapter remoteDataAdapter,
                                           InstructionDataTransformer dataTransformer,
                                           BizInstanceFieldsService bizInstanceFieldsService,
                                           BizInstanceInfoService bizInstanceInfoService,
                                           ConfMatterExtendService confMatterExtendService) {
        this.remoteDataAdapter = remoteDataAdapter;
        this.dataTransformer = dataTransformer;
        this.bizInstanceFieldsService = bizInstanceFieldsService;
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.confMatterExtendService = confMatterExtendService;
    }

    protected ComponentRunVO process(ComponentDataContext componentDataContext) {
        // 获取当前办件信息
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());

        // 使用新的属性管理方式获取配置
        String cusTips = getPropertyValue(componentDataContext, "tips");
        InstructionRemoteData instructionRemoteData = new InstructionRemoteData();

        // 获取第三方接口的提示信息
        String emptyText = getPropertyValue(componentDataContext, "emptyTip");
        String infoErrorTip = getPropertyValue(componentDataContext, "infoErrorTip");

        if (StringUtils.isNotBlank(emptyText)) {
            instructionRemoteData.setEmptyText(emptyText);
        }
        if (StringUtils.isNotBlank(infoErrorTip)) {
            instructionRemoteData.setErrorText(infoErrorTip);
        }


        String content = componentDataContext.getConfComponent().getContent();
        // 获取 init 配置
        JSONObject initConfig = remoteDataAdapter.getConfig(content, "init");
        if (initConfig == null) {
            return getComponentRunVO(instructionRemoteData, componentDataContext);
        }

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();

        // 执行远程调用
        String url = initConfig.getString("url");
        String methodStr = initConfig.getString("method");
        HttpMethod httpMethod = HttpMethod.GET; // 默认使用 GET 方法
        if (methodStr != null) {
            try {
                httpMethod = HttpMethod.valueOf(methodStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                // 使用默认方法
            }
        }

        Map<String, Object> formMap = MapUtil.<String, Object>builder()
                .put("idCard", bizInstanceInfo.getApplicationCertificateCode())
                .put("name", bizInstanceInfo.getApplicationName()).build();
        String responseBody = remoteDataAdapter.fetchData(url, httpMethod, headers, null, formMap);
        if (responseBody != null && !"[]".equals(responseBody)) {
            @SuppressWarnings("unchecked")
            List<List<?>> lists = (List<List<?>>) remoteDataAdapter.transformData(responseBody);
            List<InstructionRemoteVO> instructionRemoteVOS = new ArrayList<>();
            for (List<?> list : lists) {
                InstructionRemoteEngine.InstructionRemoteVO vo = new InstructionRemoteEngine.InstructionRemoteVO();
                vo.setSelected(false);
                vo.setList(list);
                instructionRemoteVOS.add(vo);
            }
            instructionRemoteData.setInstructionRemoteVOS(instructionRemoteVOS);
        }

        // 组装渲染数据
        ComponentRunVO componentRunVO = getComponentRunVO(instructionRemoteData, componentDataContext);
        componentRunVO.setTips(cusTips);
        return componentRunVO;
    }


    ComponentRunVO getComponentRunVO(InstructionRemoteData instructionRemoteData, ComponentDataContext componentDataContext) {
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(instructionRemoteData)
                .build();

        ComponentRunVO vo = new ComponentRunVO();
        vo.setRenderData(List.of(renderData));

        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }


    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        extracted(componentDataContext);
    }

    private void extracted(ComponentDataContext componentDataContext) {
        Object data = componentDataContext.getSubmitData();
        String content = componentDataContext.getConfComponent().getContent();

        JSONObject detailConfig = remoteDataAdapter.getConfig(content, "buttons.detail");
        if (detailConfig == null) {
            throw new IllegalArgumentException("Missing 'buttons.detail' configuration in component content");
        }

        String url = detailConfig.getString("url");
        String methodStr = detailConfig.getString("method");
        HttpMethod httpMethod = HttpMethod.POST; // 默认使用 POST 方法
        if (methodStr != null) {
            try {
                httpMethod = HttpMethod.valueOf(methodStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                // 如果传入的 method 不合法，则记录日志并使用默认方法
                System.out.println("Invalid HTTP method: " + methodStr + ", using default method: POST");
            }
        }

        if (data != null) {
            HttpHeaders headers = new HttpHeaders();
            String responseBody = remoteDataAdapter.fetchData(url, httpMethod, headers, data.toString(), null);

            if (responseBody != null) {
                JSONObject transformed = dataTransformer.transformApiResponse(responseBody);

                // 更新表单数据
                updateFormData(componentDataContext, transformed);

                // 更新缓存
                updateCache(componentDataContext, transformed);
            }
        }
    }

    private void updateFormData(ComponentDataContext componentDataContext, JSONObject transformed) {
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId());
        BizInstanceFields bizInstanceFields = bizInstanceFieldsService.
                queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
        String formObj = bizInstanceFieldsVO.getFormObj();
        if (formObj != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
            formObjMap.putAll(transformed.getJSONObject("data"));
            bizInstanceFields.setFormObj(JSON.toJSONString(formObjMap));
        } else {
            bizInstanceFields.setFormObj(JSON.toJSONString(transformed.getJSONObject("data")));
        }
        bizInstanceFieldsService.update(bizInstanceFields);
    }

    private void updateCache(ComponentDataContext componentDataContext, JSONObject transformed) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        chatProcessDTO.setApiExtractCode((Set<String>) transformed.get("codeList"));
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
    }


    @Getter
    @Setter
    static class InstructionRemoteData {
        // 没数据提示内容
        private String emptyText;
        // 有问题提示内容
        private String errorText;
        // 列表数据
        private List<InstructionRemoteEngine.InstructionRemoteVO> instructionRemoteVOS;
    }

    @Getter
    @Setter
    static class InstructionRemoteVO {
        private boolean selected;
        private List<?> list;
    }
}
