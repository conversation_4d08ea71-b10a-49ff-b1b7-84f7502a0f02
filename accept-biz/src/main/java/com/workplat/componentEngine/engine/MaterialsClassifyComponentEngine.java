package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSON;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.service.MaterialClassificationService;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.biz.converter.BizInstanceFieldsConvert;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.utils.ChatCacheUtil;
import com.workplat.utils.FormFieldFilterUtil;
import com.workplat.utils.FormStepProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 材料分类组件引擎
 * 处理单个材料的分类，主要功能包括：
 * 1. 材料分类处理
 * 2. 分类结果的临时保存
 * 3. 缓存管理
 *
 * <AUTHOR>
 * @date 2025/05/28
 */
@Slf4j
@Service
public class MaterialsClassifyComponentEngine extends AbstractMaterialClassifyEngine {

    private final String CODE = ComponentEngineCode.MATERIAL_UPLOAD;

    private final BizInstanceMaterialService bizInstanceMaterialService;

    /**
     * 构造函数
     *
     * @param materialClassificationService 材料分类服务
     * @param chatCacheUtil 聊天缓存工具
     * @param bizInstanceMaterialService 业务实例材料服务
     * @param instanceFieldsService 实例字段服务
     */
    public MaterialsClassifyComponentEngine(
            MaterialClassificationService materialClassificationService,
            ChatCacheUtil chatCacheUtil,
            BizInstanceMaterialService bizInstanceMaterialService,
            BizInstanceFieldsService instanceFieldsService,
            BizInstanceFieldsConvert bizInstanceFieldsConvert
            ) {
        super(materialClassificationService, chatCacheUtil, instanceFieldsService, bizInstanceMaterialService, bizInstanceFieldsConvert);
        this.bizInstanceMaterialService = bizInstanceMaterialService;
    }

    /**
     * 执行组件操作
     * 主要流程：
     * 1. 检查缓存中是否已有分类结果
     * 2. 如果有，直接返回缓存结果
     * 3. 如果没有，进行材料分类处理
     *
     * @return 组件运行结果
     */
    @Override
    public ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());

        // 判断是否已有文件分类结果
        if (chatProcessDTO.getMaterialSubmitVOS() != null && !chatProcessDTO.getMaterialSubmitVOS().isEmpty()) {
            ComponentRunVO vo = new ComponentRunVO();
            ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                    .componentName(CODE)
                    .componentInfo(chatProcessDTO.getMaterialSubmitVOS())
                    .build();

            vo.setRenderData(List.of(renderData));
            return vo;
        }

        // 直接查询
        List<BizInstanceMaterialVO> bizInstanceMaterialVOS = bizInstanceMaterialService.getInstanceMaterial(componentDataContext.getInstanceId());
        List<BizInstanceMaterialVO> list = bizInstanceMaterialVOS.stream().filter(bizInstanceMaterialVO ->
                !bizInstanceMaterialVO.isBackFill()).toList();
        return processMaterialClassification(new ArrayList<>(list), chatProcessDTO, componentDataContext);
    }

    /**
     * 填充数据
     * 将分类结果转换为提交DTO并临时保存
     *
     * @param componentDataContext 组件数据上下文
     */
    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        super.fillData(componentDataContext);
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        // 判断是否是材料分类组件
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());

        BizInstanceFieldsVO bizInstanceFieldsVO =
                bizInstanceFieldsService.queryByInstanceId(chatProcessDTO.getInstanceId());
        try {
            // 获取表单字段过滤结果，使用移除模式
            FormFieldFilterUtil.FormFilterResult filterResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                    bizInstanceFieldsVO.getFormJson(),
                    JSON.toJSONString(chatProcessDTO.getAiExtractFields()),
                    false // 移除模式
            );
            // 判断是否还有字段可以填写
            if (!FormStepProcessor.hasFieldsToFill(filterResult.getFilteredFormJson(), bizInstanceFieldsVO.getFieldsFilterMap())) {
                return InstructionConstant.SUCCESS_NEXT.getCode();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return super.getNextInstruction(componentDataContext);
    }
}
