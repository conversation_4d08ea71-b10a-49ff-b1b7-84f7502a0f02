package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.extend.talent.entity.HouseholdRegFile;
import com.workplat.extend.talent.service.HouseholdRegFileService;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.entity.BizInstanceMaterial;
import com.workplat.gss.application.dubbo.entity.BizInstanceMaterialFile;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialFileService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.file.entity.SysFileEntity;
import com.workplat.gss.file.service.SysFileEntityService;
import com.workplat.utils.FormFieldFilterUtil;
import lombok.Builder;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class HouseholdRegComponentEngine extends AbstractComponentEngine {

    private final BizInstanceInfoService bizInstanceInfoService;
    private final HouseholdRegFileService householdRegFileService;
    private final SysFileEntityService sysFileEntityService;
    private final BizInstanceFieldsService bizInstanceFieldsService;
    private final BizInstanceMaterialService bizInstanceMaterialService;
    private final BizInstanceMaterialFileService bizInstanceMaterialFileService;

    private final String CODE = ComponentEngineCode.HOUSEHOLD_REG;

    public HouseholdRegComponentEngine(BizInstanceInfoService bizInstanceInfoService,
                                       HouseholdRegFileService householdRegFileService,
                                       SysFileEntityService sysFileEntityService,
                                       BizInstanceFieldsService bizInstanceFieldsService,
                                       BizInstanceMaterialService bizInstanceMaterialService,
                                       BizInstanceMaterialFileService bizInstanceMaterialFileService) {
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.householdRegFileService = householdRegFileService;
        this.sysFileEntityService = sysFileEntityService;
        this.bizInstanceFieldsService = bizInstanceFieldsService;
        this.bizInstanceMaterialService = bizInstanceMaterialService;
        this.bizInstanceMaterialFileService = bizInstanceMaterialFileService;
    }

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        ComponentRunVO result = new ComponentRunVO();
        ResultData resultData = new ResultData();
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());

        String content = componentDataContext.getConfComponent().getContent();
        String existText = getPropertyValue(componentDataContext, "existText");
        String notExistText = getPropertyValue(componentDataContext, "notExistText");

        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());

        HouseholdRegFile householdRegFile =
                householdRegFileService.queryForSingle(ImmutableMap.of("=(cardId)", bizInstanceInfo.getApplicationCertificateCode() == null ? "11111" : bizInstanceInfo.getApplicationCertificateCode()));
        if (householdRegFile != null) {
            resultData.setStatus("exist");
            resultData.setData(HouseholdRegData.builder()
                    .text(existText)
                    .files(Collections.singletonList(sysFileEntityService.queryById(householdRegFile.getFileId())))
                    .build());
            // 提取字段进入
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                JsonNode fieldMapNode = objectMapper.readTree(householdRegFile.getDataJson());
                chatProcessDTO.setApiExtractCode(FormFieldFilterUtil.getFieldNamesFromMap(fieldMapNode));
                chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        } else {
            resultData.setStatus("notExist");
            resultData.setData(HouseholdRegData.builder()
                    .text(notExistText)
                    .files(null)
                    .build());
        }

        // 渲染数据
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(resultData)
                .build();

        List<ComponentRunVO.RenderData> renderDataList = Collections.singletonList(renderData);
        result.setRenderData(renderDataList);

        return result;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Data
    public static class ResultData {
        private String status;
        private HouseholdRegData data;
    }


    @Data
    @Builder
    public static class HouseholdRegData {
        private String text;
        private List<SysFileEntity> files;
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());

        HouseholdRegFile householdRegFile =
                householdRegFileService.queryForSingle(ImmutableMap.of("=(cardId)", bizInstanceInfo.getApplicationCertificateCode() == null ? "11111" : bizInstanceInfo.getApplicationCertificateCode()));
        if (householdRegFile != null) {
            // 1.将文件信息放入到对应的材料-人才落户登记表
            bizInstanceMaterialService.initInstanceMaterial(componentDataContext.getInstanceId());
            BizInstanceMaterial instanceMaterial =
                    bizInstanceMaterialService.queryForSingle(ImmutableMap.of("=(materialCode)", "szsrclhdjb", "=(instance.id)", componentDataContext.getInstanceId()));
            // 增加新的材料文件
            SysFileEntity sysFileEntity = sysFileEntityService.queryById(householdRegFile.getFileId());
            BizInstanceMaterialFile instanceMaterialFile = getBizInstanceMaterialFile(sysFileEntity, instanceMaterial);
            bizInstanceMaterialFileService.save(instanceMaterialFile);
            // 2.将表单数据放入到表单中
            bizInstanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId());
            BizInstanceFields bizInstanceFields = bizInstanceFieldsService.queryForSingle(ImmutableMap.of("=(instance.id)", componentDataContext.getInstanceId()));
            bizInstanceFields.setFormObj(householdRegFile.getDataJson());
            bizInstanceFieldsService.update(bizInstanceFields);
        }
    }

    @NotNull
    private static BizInstanceMaterialFile getBizInstanceMaterialFile(SysFileEntity sysFileEntity, BizInstanceMaterial instanceMaterial) {
        BizInstanceMaterialFile instanceMaterialFile = new BizInstanceMaterialFile();
        if (sysFileEntity != null) {
            instanceMaterialFile.setFileId(sysFileEntity.getId());
            instanceMaterialFile.setFileName(sysFileEntity.getFileName());
            instanceMaterialFile.setFileSize(sysFileEntity.getFileSize());
            instanceMaterialFile.setFileType(sysFileEntity.getFileType());
            instanceMaterialFile.setAutoGenerate(true);
        }
        instanceMaterialFile.setInstanceMaterial(instanceMaterial);
        return instanceMaterialFile;
    }
}
