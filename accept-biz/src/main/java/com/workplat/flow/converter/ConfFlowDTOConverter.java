package com.workplat.flow.converter;

import cn.hutool.core.map.MapUtil;
import com.workplat.flow.dto.ConfFlowDTO;
import com.workplat.flow.entity.ConfFlow;
import com.workplat.flow.entity.ConfFlowComponent;
import com.workplat.flow.service.ConfFlowComponentService;
import com.workplat.starter.basic.converter.BaseConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ConfFlowDTOConverter
 * @Description
 * @Date 2025/5/22
 * @Version 1.0.0
 **/
@Component
public class ConfFlowDTOConverter implements BaseConverter<ConfFlow, ConfFlowDTO> {

    @Autowired
    private ConfFlowComponentService confFlowComponentService;

    @Override
    public ConfFlowDTO convert(ConfFlow source) {
        List<ConfFlowComponent> confFlowComponents = confFlowComponentService.queryForList(MapUtil.<String, Object>builder().put("=(confFlow.id)", source.getId()).build());

        ConfFlowDTO confFlowDTO = new ConfFlowDTO();
        confFlowDTO.setId(source.getId());
        confFlowDTO.setName(source.getName());
        confFlowDTO.setCode(source.getCode());
        confFlowDTO.setDescription(source.getDescription());

        confFlowDTO.setComponents(
                confFlowComponents.stream()
                        .sorted((a, b) -> Integer.compare(a.getSequenceOrder(), b.getSequenceOrder()))
                        .map(confFlowComponent -> {
                            ConfFlowDTO.ConfComponentDTO confComponentDTO = new ConfFlowDTO.ConfComponentDTO();
                            confComponentDTO.setId(confFlowComponent.getId());
                            confComponentDTO.setSequenceOrder(confFlowComponent.getSequenceOrder());
                            confComponentDTO.setComponentId(confFlowComponent.getConfComponent().getId());
                            confComponentDTO.setConfiguration(confFlowComponent.getConfiguration());
                            confComponentDTO.setComponentName(confFlowComponent.getConfComponent().getName());
                            confComponentDTO.setComponentCode(confFlowComponent.getConfComponent().getCode());
                            confComponentDTO.setReplaceableProps(confFlowComponent.getConfComponent().getReplaceableProps());
                            return confComponentDTO;
                        }).toList()
        );

        return confFlowDTO;
    }
}
