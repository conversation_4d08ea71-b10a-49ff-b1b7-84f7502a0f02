package com.workplat.accept.business.chat.service.Impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.workplat.accept.business.chat.dto.FileClassifyDTO;
import com.workplat.accept.business.chat.dto.FileDTO;
import com.workplat.accept.business.chat.service.DifyServiceHelper;
import com.workplat.accept.business.chat.service.MattersClassifyService;
import com.workplat.accept.business.chat.vo.BlockWorkFlowVO;
import com.workplat.extend.talent.entity.HouseholdRegFile;
import com.workplat.extend.talent.service.HouseholdRegFileService;
import com.workplat.gss.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR> cheng
 * @date 2025/4/8 17:26
 */
@Service
@Slf4j
public class MattersClassifyServiceImpl implements MattersClassifyService {

    @Autowired
    DifyServiceHelper difyServiceHelper;
    @Autowired
    Environment environment;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    HouseholdRegFileService householdRegFileService;

    @Value("${file.download.url:https://servers.workplat.com/zwfw-ai-central/api/configuration/file/download?id=}")
    private String DOWNLOAD_URL;

    @Override
    public JSONArray fileClassifyBl(FileClassifyDTO dto,Map<String, Object> dataMap, String fileClassifyKey) {
        String baiLianKey = environment.getProperty("baiLian.apiKey", String.class, "sk-a482d257230a49a08107b0cffa31ac8a");
        String baiLianUploadUrl = environment.getProperty("baiLian.uploadUrl", String.class, "https://dashscope.aliyuncs.com/compatible-mode/v1/files");
        List<FileDTO> bodyList = dto.getFileUrls();
        for (FileDTO body : bodyList) {
           body.setBaiLianId(uploadFileBl(body, baiLianKey, baiLianUploadUrl));
        }
        dataMap.put("imageList", bodyList);
        return fileClassifyResult(dto, fileClassifyKey, dataMap);
    }

    @Override
    public JSONArray withdrawBl(FileClassifyDTO dto, String fileClassifyKey) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("requestContent", dto.getRecognizeContent());
        String baiLianUploadUrl = environment.getProperty("baiLian.uploadUrl", String.class, "https://dashscope.aliyuncs.com/compatible-mode/v1/files");
        String baiLianKey = environment.getProperty("baiLian.apiKey", String.class, "sk-a482d257230a49a08107b0cffa31ac8a");
        List<FileDTO> bodyList = dto.getFileUrls();
        for (FileDTO body : bodyList) {
            body.setBaiLianId(uploadFileBl(body, baiLianKey, baiLianUploadUrl));
        }
        dataMap.put("imageList", bodyList);
        return fileClassifyResult(dto, fileClassifyKey, dataMap);
    }

    @Override
    public void saveHouseReg(String fileIds) {
        String baiLianUploadUrl = environment.getProperty("baiLian.uploadUrl", String.class, "https://dashscope.aliyuncs.com/compatible-mode/v1/files");
        String baiLianKey = environment.getProperty("baiLian.apiKey", String.class, "sk-a482d257230a49a08107b0cffa31ac8a");
        String houseRegKey = environment.getProperty("houseRegFileKey", String.class, "app-0kXVKaVzO4SAaIIQNaZqBB1v");
        List<String> baiLianIds = Lists.newArrayList();
        Map<String, String> fileMap = Maps.newHashMap();
        String[] fileStr = JSON.parseObject(fileIds).getString("fileIds").split(",");
        for (String fileId : fileStr) {
            FileDTO fileDTO = new FileDTO();
            fileDTO.setName("人才落户登记表");
            fileDTO.setUrl(DOWNLOAD_URL + fileId);
            String baiLianId = uploadFileBl(fileDTO, baiLianKey, baiLianUploadUrl);
            fileMap.put(baiLianId, fileId);
            baiLianIds.add(baiLianId);
        }
        try {
            // 调用Dify服务进行字段提取
            BlockWorkFlowVO blockWorkFlowResponse = difyServiceHelper.blockingWorkflow(
                    houseRegKey,
                    houseRegKey,
                    ImmutableMap.of("input_text",  String.valueOf(JSONArray.from(baiLianIds)))
            );
            Map<String, Object> data = blockWorkFlowResponse.getData();
            if (data != null && data.containsKey("outputs")) {
                JSONObject outputs = JSON.parseObject(JSON.toJSONString(data.get("outputs")));
                JSONArray dataJson = outputs.getJSONArray("result");
                List<HouseholdRegFile> list = Lists.newArrayList();
                for (Object item : dataJson){
                    JSONObject itemJson = JSON.parseObject(item.toString());
                    JSONObject itemObj = convertHouseReg(itemJson);
                    HouseholdRegFile householdRegFile = householdRegFileService.queryForSingle(ImmutableMap.of("=(cardId)", itemObj.getString("ddrsfzhm")));
                    if (householdRegFile == null){
                        householdRegFile = new HouseholdRegFile();
                    }
                    String fileId = fileMap.get(itemJson.getString("baiLianId"));
                    householdRegFile.setCardId(itemObj.getString("ddrsfzhm"));
                    householdRegFile.setName(itemObj.getString("ddrxm"));
                    householdRegFile.setFileId(fileId);
                    householdRegFile.setDataJson(String.valueOf(itemObj));
                    list.add(householdRegFile);
                }
                householdRegFileService.saveList(list);
            } else {
                throw new BusinessException("响应数据为空或不包含outputs字段");
            }
        } catch (Exception e){
            throw new BusinessException(e);
        } finally {
            // 删除百炼文件
            baiLianIds.forEach(this::deleteFileBl);
        }

    }

    /**
     * 将文件上传百炼平台
     * @param dto
     * @param baiLianKey
     * @param baiLianUploadUrl
     * @return
     */
    String uploadFileBl(FileDTO dto, String baiLianKey, String baiLianUploadUrl){
        // 1. 获取远程文件流
        ResponseEntity<Resource> downloadResponse = restTemplate.exchange(
                dto.getUrl(),
                HttpMethod.GET,
                null,
                Resource.class
        );

        if (downloadResponse.getStatusCode() == HttpStatus.OK && downloadResponse.getBody() != null) {
            try (InputStream inputStream = downloadResponse.getBody().getInputStream()) {
                // 2. 转换为 ByteArrayResource（避免流被多次读取）
                byte[] fileBytes = IOUtils.toByteArray(inputStream);
                Resource resource = new ByteArrayResource(fileBytes) {
                    @Override
                    public String getFilename() {
                        return dto.getName();
                    }
                };

                // 3. 构建 multipart 请求
                MultiValueMap<String, Object> jsonBody = new LinkedMultiValueMap<>();
                jsonBody.add("file", resource);
                jsonBody.add("purpose", "file-extract");

                // 4. 设置请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                headers.setBearerAuth(baiLianKey);

                HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(jsonBody, headers);

                // 5. 发送请求
                ResponseEntity<String> response = restTemplate.exchange(
                        baiLianUploadUrl,
                        HttpMethod.POST,
                        requestEntity,
                        String.class
                );
                JSONObject jsonResponse = JSON.parseObject(response.getBody());
                if (jsonResponse != null) {
                    return jsonResponse.getString("id");
                }
            } catch (Exception e) {
                log.error(e.toString());
            }
        }
        return null;
    }

    @Nullable
    private JSONArray fileClassifyResult(FileClassifyDTO request,
                                         String apiKey,
                                         Map<String, Object> dataMap) {
        try {
            log.info("开始处理文件分类请求，recordId: {}", request.getChatId());

            // 调用Dify服务进行文件分类
            BlockWorkFlowVO blockWorkFlowResponse = difyServiceHelper.blockingWorkflow(
                    request.getUserId(),
                    apiKey,
                    ImmutableMap.of("input_text", JSON.toJSONString(dataMap))
            );

            Map<String, Object> data = blockWorkFlowResponse.getData();
            if (data != null && data.containsKey("outputs")) {
                Object outputsObj = data.get("outputs");
                if (outputsObj instanceof Map) {
                    Map<?, ?> outputs = (Map<?, ?>) outputsObj;
                    Object resultObj = outputs.get("result");
                    if (resultObj != null) {
                        log.info("文件分类处理成功，recordId: {}", request.getChatId());
                        return materialFileGenerator(request, resultObj);
                    } else {
                        log.warn("文件分类结果为空，recordId: {}", request.getChatId());
                    }
                } else {
                    log.warn("输出对象不是Map类型，recordId: {}", request.getChatId());
                }
            } else {
                log.warn("响应数据为空或不包含outputs字段，recordId: {}", request.getChatId());
            }
        } catch (Exception e) {
            log.error("文件分类处理失败，recordId: {}", request.getChatId(), e);
        } finally {
            // 删除百炼文件
            Object o = dataMap.get("imageList");
            if (o instanceof List) {
                List<FileDTO> bodyList = JSONArray.parseArray(JSON.toJSONString(o), FileDTO.class);
                for (FileDTO fileRequestBody : bodyList) {
                    if (fileRequestBody.getBaiLianId() != null) {
                        deleteFileBl(fileRequestBody.getBaiLianId());
                    }
                }
            }
        }
        return null;
    }

    private void deleteFileBl(String baiLianId) {
        String baiLianKey = environment.getProperty("baiLian.apiKey", String.class, "sk-a482d257230a49a08107b0cffa31ac8a");
        String deleteUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1/files/" + baiLianId;
        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setBearerAuth(baiLianKey);
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    deleteUrl,
                    HttpMethod.DELETE,
                    requestEntity,
                    String.class
            );
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("百炼文件删除失败，baiLianId: {}", baiLianId);
            }
            log.info("百炼文件删除成功，baiLianId: {}", baiLianId);
        } catch (Exception e) {
            log.error("百炼文件删除失败，baiLianId: {}", baiLianId, e);
        }
    }

    /**
     * 文件分类处理
     *
     * @param request   请求数据 包含的上传的文件信息
     * @param resultObj AI文件分类结果
     * @return map
     */
    @NotNull
    private JSONArray materialFileGenerator(FileClassifyDTO request, Object resultObj) {
        // 处理返回结果
        if (resultObj.toString().startsWith("[") && resultObj.toString().endsWith("]")){
            // 如果[,]，则去掉这个逗号
            resultObj = resultObj.toString().replace("[,", "[").replace(",]", "]");
        }

        // 解析AI返回的分类结果

        return JSONArray.parseArray(resultObj.toString());
    }

    /**
     * 提取出中文键值对
     *
     * @param resultList 结果列表
     * @return 包含中文键的键值对Map
     */
    private Map<String, Object> extractChineseKeyValues(JSONArray resultList) {
        if (resultList == null || resultList.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, Object> kvMap = new LinkedHashMap<>();
        Pattern chinesePattern = Pattern.compile("\\p{Script=Han}");

        for (Object obj : resultList) {
            if (!(obj instanceof JSONObject item)) {
                continue;
            }

            item.forEach((key, value) -> {
                // 检查键是否包含中文字符且值不为空
                if (value != null && StringUtils.isNotBlank(value.toString()) &&
                        chinesePattern.matcher(key).find()) {
                    kvMap.put(key, value);
                }
            });
        }

        return kvMap;
    }


    private JSONObject convertHouseReg(JSONObject item){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ddrxm",item.getString("调动人姓名"));
        jsonObject.put("ddrsfzhm",item.getString("居民身份证号码"));
        jsonObject.put("lxdh",item.getString("联系电话"));
        jsonObject.put("qcdz",item.getString("住址或单位"));
        jsonObject.put("qcdhkdjjg",item.getString("户口登记机关"));
        if (item.getString("迁入地所属县分局") != null){
            jsonObject.put("qrdssxfj",item.getString("迁入地所属县分局"));
        }
        if (item.getString("迁入地所属派出所") != null){
            jsonObject.put("qrdsspcs",item.getString("迁入地所属派出所"));
        }
        jsonObject.put("qrdz",item.getString("迁入地址或单位地址"));
        jsonObject.put("lhlx",item.getString("人才落户申报类型"));
        JSONArray sqry = new JSONArray();
        JSONArray jsonArray = item.getJSONArray("随迁人员");
        if (jsonArray != null) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("sqry", jsonArray.getJSONObject(i).getString("随迁人员姓名"));
                jsonObject1.put("ysqrgx", jsonArray.getJSONObject(i).getString("随迁人员与调动人关系"));
                jsonObject1.put("sqrysfzhm", jsonArray.getJSONObject(i).getString("随迁人员居民身份证号码"));
                sqry.add(jsonObject1);
            }
        }
        if (!sqry.isEmpty()){
            jsonObject.put("sqrylb_table", sqry);
        }
        return jsonObject;
    }



}
