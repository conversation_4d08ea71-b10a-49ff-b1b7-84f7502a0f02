package com.workplat.accept.business.chat.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.dto.FileClassifyDTO;
import com.workplat.accept.business.chat.dto.FileDTO;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialFileVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.ext.dto.BizInstanceMaterialExtendJson;
import com.workplat.gss.file.entity.SysFileEntity;
import com.workplat.gss.file.service.SysFileEntityService;
import com.workplat.gss.service.item.dubbo.matter.vo.accept.ConfMatterAcceptFieldsVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 材料分类服务
 * 处理材料分类的核心逻辑，包括文件分类、字段提取和未分类文件处理
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
@Slf4j
@Service
public class MaterialClassificationService {

    private final MattersClassifyService mattersClassifyService;
    private final SysFileEntityService sysFileEntityService;

    @Value("${file.download.url:https://servers.workplat.com/zwfw-ai-central/api/configuration/file/download?id=}")
    private String DOWNLOAD_URL;

    /**
     * 构造函数
     *
     * @param mattersClassifyService 事项分类服务
     * @param sysFileEntityService 系统文件实体服务
     */
    public MaterialClassificationService(MattersClassifyService mattersClassifyService,
                                      SysFileEntityService sysFileEntityService) {
        this.mattersClassifyService = mattersClassifyService;
        this.sysFileEntityService = sysFileEntityService;
    }

    /**
     * 处理材料分类
     * 主要流程：
     * 1. 准备数据映射
     * 2. 调用文件分类服务
     * 3. 处理分类结果
     * 4. 过滤已有字段
     * 5. 处理未分类文件
     *
     * @param fileClassifyDTO 文件分类DTO
     * @param bizInstanceMaterialVOS 业务实例材料列表
     * @param bizInstanceFieldsVO 业务实例字段
     * @param materialsClassifyKey 材料分类key
     * @return 分类结果，包含处理后的材料列表和提取的字段
     */
    public MaterialClassificationResult processClassification(
            FileClassifyDTO fileClassifyDTO,
            List<BizInstanceMaterialVO> bizInstanceMaterialVOS,
            BizInstanceFieldsVO bizInstanceFieldsVO,
            String materialsClassifyKey) {

        Map<String, Object> dataMap = prepareDataMap(bizInstanceMaterialVOS, bizInstanceFieldsVO.getFieldJsonObject());

        // 存储文件分类结果和字段提取结果
        Map<String, Object> fileClassifyDataMap = new HashMap<>();
        Map<String, Object> fieldExtractDataMap = new HashMap<>();

        // 获取文件分类结果
        JSONArray jsonArray = classifyFiles(fileClassifyDTO, dataMap, materialsClassifyKey);
        log.info("processClassification - 文件分类结果: {}", JSON.toJSONString(jsonArray));

        // 处理分类文件
        List<JSONObject> processedObjects = processClassifiedFiles(
                bizInstanceMaterialVOS,
                jsonArray,
                fileClassifyDataMap,
                fieldExtractDataMap,
                bizInstanceFieldsVO.getFieldJsonObject());

        // 过滤已有的填写值
        filterExistingFields(fieldExtractDataMap, bizInstanceFieldsVO);

        // 删除已处理的数据并处理未分类文件
        JSONArray imageArray = filterUnprocessedImages(dataMap, processedObjects);

        // 处理未分类文件
        if (!imageArray.isEmpty()) {
            BizInstanceMaterialVO undefinedMaterial = createUndefinedMaterial(imageArray, fileClassifyDataMap);
            bizInstanceMaterialVOS.add(undefinedMaterial);
        }

        return new MaterialClassificationResult(bizInstanceMaterialVOS, fieldExtractDataMap);
    }

    private static JSONArray filterUnprocessedImages(Map<String, Object> dataMap, List<JSONObject> processedObjects) {
        Object imageList = dataMap.get("imageList");
        if (imageList instanceof List<?> imageArray) {
            if (!imageArray.isEmpty()) {
                // 提取已处理的URL集合
                Set<String> processedUrls = new HashSet<>();
                for (JSONObject obj : processedObjects) {
                    String url = obj.getString("url");
                    if (url != null && !url.isEmpty()) {
                        processedUrls.add(url);
                    }
                }

                // 倒序遍历删除已处理的文件
                for (int i = imageArray.size() - 1; i >= 0; i--) {
                    FileDTO fileDTO = JSONObject.parseObject(JSON.toJSONString(imageArray.get(i)), FileDTO.class);
                    String url = fileDTO.getUrl();
                    
                    if (url != null && !url.isEmpty() && processedUrls.contains(url)) {
                        imageArray.remove(i);
                    }
                }
                return new JSONArray(imageArray);
            }
        }
        return new JSONArray();
    }

    /**
     * 准备数据映射
     * 将业务实例材料列表转换为材料映射数据
     *
     * @param bizInstanceMaterialVOS 业务实例材料列表
     * @param fieldJsonObjectList 字段JSON对象列表
     * @return 数据映射
     */
    private Map<String, Object> prepareDataMap(List<BizInstanceMaterialVO> bizInstanceMaterialVOS,
                                             List<ConfMatterAcceptFieldsVO> fieldJsonObjectList) {
        Map<String, Object> dataMap = new HashMap<>();
        List<Map<String, String>> mapList = bizInstanceMaterialVOS.stream()
                .map(bizInstanceMaterialVO -> createMaterialMap(bizInstanceMaterialVO, fieldJsonObjectList))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        dataMap.put("materialMap", mapList);
        return dataMap;
    }

    /**
     * 创建材料映射
     * 将单个业务实例材料转换为映射数据
     *
     * @param bizInstanceMaterialVO 业务实例材料VO
     * @param fieldJsonObjectList 字段JSON对象列表
     * @return 材料映射数据
     */
    private Map<String, String> createMaterialMap(BizInstanceMaterialVO bizInstanceMaterialVO,
                                                List<ConfMatterAcceptFieldsVO> fieldJsonObjectList) {
        String extendJson = bizInstanceMaterialVO.getExtendJson();
        BizInstanceMaterialExtendJson materialExtendJson = BizInstanceMaterialExtendJson.convert(extendJson);
        String materialBindFields = materialExtendJson.getMaterialBindFields();
        // 只分类有别名的材料
        if (StringUtils.isBlank(bizInstanceMaterialVO.getMaterialAliasName())){
            return null;
        }
        Map<String, String> item = new HashMap<>();
        item.put("name", bizInstanceMaterialVO.getMaterialAliasName());
        item.put("explain", materialExtendJson.getPrompt() == null ? "" : materialExtendJson.getPrompt());
        if (materialBindFields == null || materialBindFields.isEmpty()) {
            item.put("fields", "");
            return item;
        }

        Collection<String> fieldNames = Arrays.stream(materialBindFields.split(","))
                .map(code -> fieldJsonObjectList.stream()
                        .filter(f -> f.getFieldCode().equals(code))
                        .map(ConfMatterAcceptFieldsVO::getFieldAlias)
                        .findFirst()
                        .orElse(null))
                .filter(Objects::nonNull)
                .toList();

        item.put("fields", String.join(",", fieldNames));
        return item;
    }

    /**
     * 调用文件分类服务进行分类
     *
     * @param dto 文件分类DTO
     * @param dataMap 数据映射
     * @param materialsClassifyKey 材料分类key
     * @return 分类结果JSON数组
     */
    private JSONArray classifyFiles(FileClassifyDTO dto, Map<String, Object> dataMap, String materialsClassifyKey) {
        try {
            JSONArray result = mattersClassifyService.fileClassifyBl(dto, dataMap, materialsClassifyKey);
            return result != null ? result : new JSONArray();
        } catch (Exception e) {
            log.error("文件分类服务调用失败", e);
            return new JSONArray();
        }
    }

    /**
     * 处理分类文件
     * 将分类结果与业务实例材料进行匹配和处理
     *
     * @param bizInstanceMaterialVOS 业务实例材料列表
     * @param jsonArray 分类结果JSON数组
     * @param fileClassifyDataMap 文件分类数据映射
     * @param fieldExtractDataMap 字段提取数据映射
     * @param fieldJsonObjectList 字段JSON对象列表
     * @return 已处理的对象列表
     */
    private List<JSONObject> processClassifiedFiles(List<BizInstanceMaterialVO> bizInstanceMaterialVOS,
                                                  JSONArray jsonArray,
                                                  Map<String, Object> fileClassifyDataMap,
                                                  Map<String, Object> fieldExtractDataMap,
                                                  List<ConfMatterAcceptFieldsVO> fieldJsonObjectList) {
        List<JSONObject> processedObjects = new ArrayList<>();
        Map<String, ConfMatterAcceptFieldsVO> fieldAliasMap = createFieldAliasMap(fieldJsonObjectList);

        for (BizInstanceMaterialVO item : bizInstanceMaterialVOS) {
            String aliasName = item.getMaterialAliasName();
            List<JSONObject> matchedObjects = findMatchedObjects(jsonArray, aliasName);

            if (matchedObjects.isEmpty()) {
                continue;
            }

            processMatchedObjects(item, matchedObjects, fileClassifyDataMap, fieldExtractDataMap, fieldAliasMap);
            processedObjects.addAll(matchedObjects);
        }

        return processedObjects;
    }

    /**
     * 创建字段别名映射
     *
     * @param fieldJsonObjectList 字段JSON对象列表
     * @return 字段别名到字段对象的映射
     */
    private Map<String, ConfMatterAcceptFieldsVO> createFieldAliasMap(List<ConfMatterAcceptFieldsVO> fieldJsonObjectList) {
        return fieldJsonObjectList.stream()
                .collect(Collectors.toMap(ConfMatterAcceptFieldsVO::getFieldAlias, Function.identity()));
    }

    /**
     * 查找匹配的对象
     *
     * @param jsonArray JSON数组
     * @param aliasName 别名
     * @return 匹配的对象列表
     */
    private List<JSONObject> findMatchedObjects(JSONArray jsonArray, String aliasName) {
        return jsonArray.stream()
                .filter(JSONObject.class::isInstance)
                .map(JSONObject.class::cast)
                .filter(json -> aliasName.contains(json.getString("name")))
                .toList();
    }

    /**
     * 处理匹配的对象
     *
     * @param item 业务实例材料VO
     * @param matchedObjects 匹配的对象列表
     * @param fileClassifyDataMap 文件分类数据映射
     * @param fieldExtractDataMap 字段提取数据映射
     * @param fieldAliasMap 字段别名映射
     */
    private void processMatchedObjects(BizInstanceMaterialVO item,
                                     List<JSONObject> matchedObjects,
                                     Map<String, Object> fileClassifyDataMap,
                                     Map<String, Object> fieldExtractDataMap,
                                     Map<String, ConfMatterAcceptFieldsVO> fieldAliasMap) {
        List<BizInstanceMaterialFileVO> fileVOS = item.getMaterialFileVOList();
        // 如果材料文件列表为空，则创建一个新的列表
        if (fileVOS == null) {
            fileVOS = new ArrayList<>();
        }
        for (JSONObject json : matchedObjects) {
            BizInstanceMaterialFileVO bizInstanceMaterialFileVO = getMaterialFileVO(json, fileClassifyDataMap);
            fileVOS.add(bizInstanceMaterialFileVO);

            extractFields(json, fieldAliasMap, fieldExtractDataMap);
        }

        item.setMaterialFileVOList(fileVOS);
    }

    /**
     * 提取字段
     *
     * @param json JSON对象
     * @param fieldAliasMap 字段别名映射
     * @param fieldExtractDataMap 字段提取数据映射
     */
    private void extractFields(JSONObject json,
                             Map<String, ConfMatterAcceptFieldsVO> fieldAliasMap,
                             Map<String, Object> fieldExtractDataMap) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("aiRecognizedFields", json);
        logData.put("fieldAliasMapKeys", fieldAliasMap.keySet());
        log.info("{}", JSON.toJSONString(logData));
        json.forEach((key, value) -> {
            ConfMatterAcceptFieldsVO field = fieldAliasMap.get(key);
            if (field != null && StringUtils.isNotBlank(String.valueOf(value))) {
                fieldExtractDataMap.put(field.getFieldCode(), value);
            }
        });
    }

    /**
     * 过滤已存在的字段
     *
     * @param fieldExtractDataMap 字段提取数据映射
     * @param bizInstanceFieldsVO 业务实例字段VO
     */
    private void filterExistingFields(Map<String, Object> fieldExtractDataMap, BizInstanceFieldsVO bizInstanceFieldsVO) {
        if (bizInstanceFieldsVO.getFormObj() != null) {
            Map formObjMap = JSON.parseObject(bizInstanceFieldsVO.getFormObj(), Map.class);
            fieldExtractDataMap.entrySet().removeIf(entry -> formObjMap.containsKey(entry.getKey()));
        }
    }

    /**
     * 创建未分类材料
     *
     * @param jsonArray JSON数组
     * @param fileClassifyDataMap 文件分类数据映射
     * @return 未分类的业务实例材料VO
     */
    private BizInstanceMaterialVO createUndefinedMaterial(JSONArray jsonArray,
                                                         Map<String, Object> fileClassifyDataMap) {
        BizInstanceMaterialVO bizInstanceMaterialVO = new BizInstanceMaterialVO();
        bizInstanceMaterialVO.setMaterialName("未分类");
        bizInstanceMaterialVO.setMaterialCode("undefined");

        List<BizInstanceMaterialFileVO> fileVOS = new ArrayList<>();
        for (Object object : jsonArray) {
            JSONObject json = JSONObject.parseObject(JSON.toJSONString(object));
            BizInstanceMaterialFileVO bizInstanceMaterialFileVO = getMaterialFileVO(json, fileClassifyDataMap);
            fileVOS.add(bizInstanceMaterialFileVO);
        }
        bizInstanceMaterialVO.setMaterialFileVOList(fileVOS);
        return bizInstanceMaterialVO;
    }

    /**
     * 获取材料文件VO
     *
     * @param object JSON对象
     * @param fileClassifyDataMap 文件分类数据映射
     * @return 业务实例材料文件VO
     */
    @NotNull
    private BizInstanceMaterialFileVO getMaterialFileVO(JSONObject object, Map<String, Object> fileClassifyDataMap) {
        String fileUrl = object.getString("url");
        String fileId;
        try {
            int lastIndex = fileUrl.lastIndexOf("=");
            fileId = lastIndex >= 0 ? fileUrl.substring(lastIndex + 1) : fileUrl;
        } catch (Exception e) {
            fileId = "unknown";
        }

        SysFileEntity sysFileEntity = sysFileEntityService.queryById(fileId);

        BizInstanceMaterialFileVO bizInstanceMaterialFileVO = new BizInstanceMaterialFileVO();
        bizInstanceMaterialFileVO.setFileId(fileId);
        bizInstanceMaterialFileVO.setFileName(sysFileEntity.getFileName());
        bizInstanceMaterialFileVO.setFileSize(sysFileEntity.getFileSize());
        bizInstanceMaterialFileVO.setFileType(sysFileEntity.getFileType());
        bizInstanceMaterialFileVO.setFileSrc(fileUrl);

        fileClassifyDataMap.put(fileId, object);
        return bizInstanceMaterialFileVO;
    }

    /**
     * 处理材料识别过程
     * 该方法主要负责根据提供的业务实例材料和字段信息，通过文件识别技术提取和分类相关信息
     *
     * @param bizInstanceMaterialVOS 业务实例材料列表，包含待处理的材料信息
     * @param bizInstanceFieldsVO    业务实例字段信息，包含字段的配置和JSON对象
     * @param fileRecognitionKey     文件识别所需的密钥，用于调用文件分类服务
     * @return 返回材料分类结果对象，包含提取的字段数据
     */
    public MaterialClassificationResult processRecognition(List<BizInstanceMaterialVO> bizInstanceMaterialVOS,
                                                           BizInstanceFieldsVO bizInstanceFieldsVO,
                                                           String fileRecognitionKey) {

        // 创建字段别名映射，以便在提取数据时匹配对应的字段
        Map<String, ConfMatterAcceptFieldsVO> fieldAliasMap = createFieldAliasMap(bizInstanceFieldsVO.getFieldJsonObject());

        // 初始化一个映射，用于存储提取的字段数据
        Map<String, Object> fieldExtractDataMap = new HashMap<>();

        // 使用并行流提高处理效率
        bizInstanceMaterialVOS.parallelStream().forEach(item -> {
            log.info("开始处理材料识别，材料名称: {}", item.getMaterialName());
            // 根据材料和字段信息创建材料映射
            Map<String, String> materialMap = createMaterialMap(item, bizInstanceFieldsVO.getFieldJsonObject());
            // 组装文件DO
            if ((materialMap != null ? materialMap.get("fields") : null) == null) {
                return;
            }
            FileClassifyDTO dto = FileClassifyDTO.builder()
                    .userId(bizInstanceFieldsVO.getInstanceId())
                    .recognizeContent(materialMap.get("fields"))
                    .fileUrls(item.getMaterialFileVOList()
                            .stream().filter(Objects::nonNull).map(v -> {
                                FileDTO fileDTO = new FileDTO();
                                fileDTO.setId(v.getFileId());
                                fileDTO.setName(v.getFileName());
                                fileDTO.setUrl(DOWNLOAD_URL + v.getFileId());
                                return fileDTO;
                            }).toList())
                    .build();
            try {
                // 调用文件分类服务进行字段提取
                JSONArray result = mattersClassifyService.withdrawBl(dto, fileRecognitionKey);
                if (result != null && !result.isEmpty()) {
                    result.forEach(obj -> {
                        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(obj));
                        // 提取字段并更新字段提取数据映射
                        extractFields(jsonObject, fieldAliasMap, fieldExtractDataMap);
                    });
                }
                log.info("材料识别处理成功，材料名称: {}", item.getMaterialName());
            } catch (Exception e) {
                // 日志记录文件分类服务调用失败的情况
                log.error("文件分类服务调用失败", e);
            }
        });

        // 过滤已存在的字段，避免重复提取
        filterExistingFields(fieldExtractDataMap, bizInstanceFieldsVO);

        // 返回材料分类结果对象，包含提取的字段数据
        return new MaterialClassificationResult(null, fieldExtractDataMap);
    }


    /**
     * 材料分类结果
     * 封装了分类处理的最终结果
     */
    @Getter
    public static class MaterialClassificationResult {
        /**
         * -- GETTER --
         *  获取处理后的材料列表
         *
         * @return 材料列表
         */
        private final List<BizInstanceMaterialVO> materialVOS;
        /**
         * -- GETTER --
         *  获取提取的字段
         *
         * @return 字段映射
         */
        private final Map<String, Object> extractedFields;

        /**
         * 构造函数
         *
         * @param materialVOS 处理后的材料列表
         * @param extractedFields 提取的字段
         */
        public MaterialClassificationResult(List<BizInstanceMaterialVO> materialVOS,
                                         Map<String, Object> extractedFields) {
            this.materialVOS = materialVOS;
            this.extractedFields = extractedFields;
        }

    }
} 