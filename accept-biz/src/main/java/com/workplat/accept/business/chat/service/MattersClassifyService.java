package com.workplat.accept.business.chat.service;

import com.alibaba.fastjson2.JSONArray;
import com.workplat.accept.business.chat.dto.FileClassifyDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/8 17:26
 */
public interface MattersClassifyService {

    /**
     * 文件分类识别-百炼
     *
     * @param dto
     * @param fileClassifyKey
     * @return
     */
     JSONArray fileClassifyBl(FileClassifyDTO dto,Map<String, Object> dataMap, String fileClassifyKey);

    /**
     * 文件字段内容提取-百炼
     *
     * @param dto
     * @param fileClassifyKey
     * @return
     */
    JSONArray withdrawBl(FileClassifyDTO dto, String fileClassifyKey);

    /**
     * 提取人才落户登记表字段信息并保存 - 百炼
     * @param fileIds
     */
    void saveHouseReg(String fileIds);


}
