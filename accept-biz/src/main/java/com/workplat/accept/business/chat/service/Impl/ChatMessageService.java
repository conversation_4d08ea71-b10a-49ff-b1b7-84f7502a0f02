package com.workplat.accept.business.chat.service.Impl;

import com.workplat.accept.business.chat.dto.BizChatMessageDTO;
import com.workplat.accept.business.chat.event.ChatMessageEvent;
import com.workplat.accept.business.chat.service.BizChatMessageService;
import com.workplat.accept.business.chat.vo.BizChatMessageVO;
import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.gss.common.core.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ChatMessageService {

    private final BizChatMessageService bizChatMessageService;
    private final ApplicationEventPublisher eventPublisher;

    public ChatMessageService(BizChatMessageService bizChatMessageService,
                              ApplicationEventPublisher eventPublisher) {
        this.bizChatMessageService = bizChatMessageService;
        this.eventPublisher = eventPublisher;
    }

    // 不记录的匹配关键字
    private static final String[] IGNORE_KEYWORDS = {"app-","flowCode","@@", "我要办理"};


    public void saveUserMessage(String conversationId, String content) {
        try {
            if (shouldIgnoreMessage(content)) return;
            ChatMessageEvent event = ChatMessageEvent.builder()
                    .conversationId(conversationId)
                    .content(content)
                    .sender("USER")
                    .timestamp(System.currentTimeMillis())
                    .build();

            eventPublisher.publishEvent(event);
            log.info("Published user message event for conversation: {}", conversationId);
        } catch (Exception e) {
            log.error("Error publishing user message event: {}", e.getMessage(), e);
        }
    }

    private static boolean shouldIgnoreMessage(String content) {
        for (String keyword : IGNORE_KEYWORDS) {
            if (content.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    public void saveAiMessage(String conversationId, String content) {
        try {
            if (shouldIgnoreMessage(content)) return;
            ChatMessageEvent event = ChatMessageEvent.builder()
                    .conversationId(conversationId)
                    .content(content)
                    .sender("ASSISTANT")
                    .timestamp(System.currentTimeMillis())
                    .build();

            eventPublisher.publishEvent(event);
            log.info("Published AI message event for conversation: {}", conversationId);
        } catch (Exception e) {
            log.error("Error publishing AI message event: {}", e.getMessage(), e);
        }
    }

    public ResponseData<List<BizChatMessageVO>> getConversationMessages(BizChatMessageDTO dto) {
        List<BizChatMessageVO> chatMessages = bizChatMessageService.getMessages(dto.getConversationId());
        return ResponseData.success(chatMessages);
    }

    public void UpdateComponentRun(ComponentRunDTO componentRunDto) {
        if (componentRunDto.getRenderData() != null
                && !componentRunDto.getRenderData().toString().equals("{}")) {
            bizChatMessageService.UpdateComponentRun(componentRunDto);
        }
    }

    public ResponseData<Void> recordChat(BizChatMessageDTO dto) {
        saveUserMessage(dto.getConversationId(), dto.getQuestion());
        saveAiMessage(dto.getConversationId(), dto.getAnswer());
        return ResponseData.success().build();
    }
}