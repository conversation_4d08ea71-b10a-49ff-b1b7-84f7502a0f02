package com.workplat.accept.business.chat.api.Impl;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.api.TestApi;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoDetailVO;
import com.workplat.push.ga.service.GaDeclarePushService;
import com.workplat.push.power.service.MattersDeclareService;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;


@RestController
@RequestMapping(value = "/api/test")
public class TestApiImpl  implements TestApi {

    private final BizInstanceInfoService bizInstanceInfoService;
    private final MattersDeclareService mattersDeclareService;
    private final JdbcTemplate jdbcTemplate;
    private final GaDeclarePushService gaDeclarePushService;

    public TestApiImpl(BizInstanceInfoService bizInstanceInfoService,
                       MattersDeclareService mattersDeclareService,
                       JdbcTemplate jdbcTemplate,
                       GaDeclarePushService gaDeclarePushService) {
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.mattersDeclareService = mattersDeclareService;
        this.jdbcTemplate = jdbcTemplate;
        this.gaDeclarePushService = gaDeclarePushService;
    }

    public String test() {
        BizInstanceInfoDetailVO detailVOById = bizInstanceInfoService.getDetailVOById("********************************");
        JSONObject itemInfoJson = JSONObject.parseObject("{\n" +
                "                      \"taskId\": \"0223638c000000006f45f507ffffa8f6\",\n" +
                "                      \"materials\": {\n" +
                "                        \"0223638c000000006fb551bd00000074\": \"lwpqjyxksqs\",\n" +
                "                        \"0223638c000000006fb4ba8f00000075\": \"jylwpaywzkgzcns\",\n" +
                "                        \"0223638c000000006fb5c0af00000077\": \"0223638c000000006742fef900001377\",\n" +
                "                        \"0223638c000000006fb7116a00000078\": \"0223638c000000006744218100001372,0223638c000000006744218100001371\",\n" +
                "                        \"0223638c000000006fb7434100000079\": \"sqblxzxksxwts\"\n" +
                "                      }\n" +
                "                    }");
        mattersDeclareService.executed(itemInfoJson, detailVOById);
        return "success";
    }

    @Override
    public JSONObject test2(String instanceId, Boolean isFirstSubmit) {
        gaDeclarePushService.push(instanceId, true);
        return null;
    }
    
    @Override
    public JSONObject testDistributionDriver(String instanceId) {
        try {
            // 通过反射调用GaDistributionDriverLoaderImpl的distributionDriver方法
            Class<?> driverClass = Class.forName("com.workplat.electronic.certificate.GaDistributionDriverLoaderImpl");
            Object driverInstance = driverClass.newInstance();
            
            // 创建DistributionDriverInput对象
            Class<?> inputClass = Class.forName("com.workplat.gss.common.script.model.declare.DistributionDriverInput");
            Object inputInstance = inputClass.newInstance();
            
            // 设置instanceId
            Method setInstanceIdMethod = inputClass.getMethod("setInstanceId", String.class);
            setInstanceIdMethod.invoke(inputInstance, instanceId);
            
            // 调用distributionDriver方法
            Method distributionDriverMethod = driverClass.getMethod("distributionDriver", inputClass);
            Object result = distributionDriverMethod.invoke(driverInstance, inputInstance);
            
            // 将结果转换为JSONObject返回
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", true);
            jsonObject.put("result", result);
            return jsonObject;
        } catch (Exception e) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", false);
            jsonObject.put("error", e.getMessage());
            return jsonObject;
        }
    }
}
