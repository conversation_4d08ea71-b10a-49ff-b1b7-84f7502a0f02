package com.workplat.accept.user.util;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.gateway.client.GatewayClientRequest;
import com.workplat.gss.admin.modules.authencation.vo.UserVO;
import com.workplat.gss.common.core.util.Sm4Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * @Author: Odin
 * @Date: 2024/9/27 19:07
 * @Description:
 */

@Slf4j
@Component
public class GatewayUtils {

    private static String allinoneUrlPrefix = "https://zwfw.tcsjj.cn/gateway-api/allinone-api";

    private static String gatewayApiUrl = "https://zwfw.tcsjj.cn/gateway-api";

    private static final GatewayClientRequest gatewayClientRequest = GatewayClientRequest.builder(

            "https://zwfw.tcsjj.cn/gateway-api",

            "CDB68E6C16144A0FAE4A376D798F70FA",

            "042f57787a1e2ce1b6271f444791114aa786d0f49bbc59fd9a0317aa39ab67132e9f63c210f70b750436357827063b18f071ff04cb53a4d6d1f6a41382c08b0b2e",

            "",

            ""

    );

    public static String getAllinoneApiResult(String url, Map<String, Object> formMap) {
        log.info("请求allinone接口：{}", allinoneUrlPrefix + url);
        String res = gatewayClientRequest.execute(HttpRequest.post(allinoneUrlPrefix + url).form(formMap), false, false);
        log.info("allinone接口 - {} -返回结果：{}", allinoneUrlPrefix + url, res);
        return res;
    }

    /**
     * 发起GET请求
     *
     * @param url        请求地址
     * @param header     请求头信息
     * @param params     URL参数
     * @param encrypt    是否加密
     * @param verifySign 是否验证签名
     * @return 响应结果
     */
    public static String executeGetRequest(String url, Map<String, String> header, Map<String, Object> params,
                                           Boolean encrypt, Boolean verifySign) {
        return executeRequest(url, header, Method.GET, null, params, encrypt, verifySign);
    }

    /**
     * 发起POST请求（请求体为JSON字符串）
     *
     * @param url        请求地址
     * @param header     请求头信息
     * @param body       请求体
     * @param encrypt    是否加密
     * @param verifySign 是否验证签名
     * @return 响应结果
     */
    public static String executePostToBodyRequest(String url, Map<String, String> header,
                                                  String body, Boolean encrypt, Boolean verifySign) {
        return executeRequest(url, header, Method.POST, body, null, encrypt, verifySign);
    }

    /**
     * 发起POST请求（表单格式）
     *
     * @param url        请求地址
     * @param header     请求头信息
     * @param form       表单数据
     * @param encrypt    是否加密
     * @param verifySign 是否验证签名
     * @return 响应结果
     */
    public static String executePostToFormRequest(String url, Map<String, String> header, Map<String, Object> form,
                                                  Boolean encrypt, Boolean verifySign) {
        return executeRequest(url, header, Method.POST, null, form, encrypt, verifySign);
    }

    /**
     * 执行HTTP请求
     *
     * @param url        请求地址
     * @param header     请求头信息
     * @param method     请求方法
     * @param body       请求体
     * @param form       表单数据
     * @param encrypt    是否加密
     * @param verifySign 是否验证签名
     * @return 响应结果（data字段内容优先）
     */
    private static String executeRequest(String url, Map<String, String> header, Method method, String body,
                                         Map<String, Object> form, Boolean encrypt, Boolean verifySign) {
        HttpRequest httpRequest = HttpRequest.of(gatewayApiUrl + url);
        httpRequest.method(method);

        // 设置请求头
        if (header != null && !header.isEmpty()) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                httpRequest.header(entry.getKey(), entry.getValue());
            }
        }

        // 设置请求体
        if (StrUtil.isNotBlank(body)) {
            httpRequest.body(body);
        }

        // 设置表单数据
        if (form != null && !form.isEmpty()) {
            httpRequest.form(form);
        }

        try {
            String execute = gatewayClientRequest.execute(httpRequest, encrypt, verifySign);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            if (jsonObject != null) {
                if (jsonObject.get("data") != null) {
                    return jsonObject.get("data").toString();
                }
                return jsonObject.toString();
            }
        } catch (Exception e) {
            log.error("请求失败", e);
        }
        return null;
    }

    public static void main(String[] args) {
        //        Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", "140502199401239568").put("name", "刘鑫").build();
//        Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", "320522199804092416").put("name", "陈雨杰").build();
//        Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", "342425199511054079").put("name", "倪健").build();
        Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", "342401199609112520").put("name", "张俊").build();

//        Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", "610431199905063016").put("name", "杨帆").build();



        String token = JSONUtil.parseObj(GatewayUtils.getAllinoneApiResult("/api/zzj/selfServiceLogin", formMap)).getStr("token");
        System.out.println(token);

        //String allinoneApiResult = GatewayUtils.executeGetRequest("/allinone-api/api/ucenter/user/get",
        //        MapUtil.<String, String>builder().put(Header.AUTHORIZATION.getValue(), "Bearer " + token).build(),
        //        null, false, false);
        //System.out.println(allinoneApiResult);
        //
        //Map<String, Object> params = new HashMap<>();
        //params.put("status", "all");
        //params.put("page", 1);
        //params.put("limit", 4);
        //params.put("sort", "businessTime,desc");
        //String executeRequest = GatewayUtils.executeGetRequest("/allinone-api/api/aioBusiness/findPage",
        //        ImmutableMap.of(Header.AUTHORIZATION.getValue(), "Bearer " + token),
        //        params, false, false);
        //JSONObject jsonObject = JSONObject.parseObject(executeRequest);
        //System.out.printf("%s", jsonObject.toJSONString());
//        String string = GatewayUtils.executeGetRequest("/bdc-ai/app/ai/blbb/getIncrementalContracts",
//                null , formMap, false, false);
//        System.out.println(string);
    }

}
