{"tableName": "", "renderList": [{"id": "4CuZB2vyB2", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "9V1ghizT26", "icon": "icon-kaiguan3", "name": "人脸识别", "type": "faceRecognition", "child": [{"id": "V2hSse6fu", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "cbe703f408794fcf8c6e29a780bf2d7a", "key": "<PERSON><PERSON><PERSON>", "field": "<PERSON><PERSON><PERSON>", "label": "父亲姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputfatherName", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsfatherName", "name": "foucs事件", "value": ""}, {"key": "change<PERSON><PERSON><PERSON>", "name": "change事件", "value": ""}, {"key": "clickfatherName", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "父亲姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "pbQenH2WC", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "235eaa5fd4ca47eca34d07342bc96eb7", "key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "父亲身份证号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputfatherCardNum", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsfatherCardNum", "name": "foucs事件", "value": ""}, {"key": "changefatherCardNum", "name": "change事件", "value": ""}, {"key": "clickfatherCardNum", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "父亲身份证号码不能为空", "trigger": "blur", "required": true}, {"message": "父亲身份证号码格式不正确", "pattern": "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[xX])$", "trigger": "blur"}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "3Z2UD7Sl7", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "c023fa0a6168499d941868a04397de90", "key": "<PERSON><PERSON><PERSON>", "field": "<PERSON><PERSON><PERSON>", "label": "母亲姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmotherName", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmotherName", "name": "foucs事件", "value": ""}, {"key": "changemotherName", "name": "change事件", "value": ""}, {"key": "clickmotherName", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "母亲姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "KRfPZz0x5", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "1792850f5a4b43e18ab36b16f5ae7f80", "key": "motherCardNum", "field": "motherCardNum", "label": "母亲身份证号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmotherCardNum", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmotherCardNum", "name": "foucs事件", "value": ""}, {"key": "changemotherCardNum", "name": "change事件", "value": ""}, {"key": "clickmotherCardNum", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "母亲身份证号码不能为空", "trigger": "blur", "required": true}, {"message": "母亲身份证号码格式不正确", "pattern": "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[xX])$", "trigger": "blur"}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "zE_j_iRan", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "0332593edc954c06873f566ba1b1488a", "key": "<PERSON><PERSON><PERSON>", "field": "<PERSON><PERSON><PERSON>", "label": "独生子女姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputchildrenName", "name": "input事件", "value": "console.log(form)"}, {"key": "foucschildrenName", "name": "foucs事件", "value": ""}, {"key": "changechildrenName", "name": "change事件", "value": ""}, {"key": "clickchildrenName", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "独生子女姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "ytOnfRWr9", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "41dc9e1c05c7495b9a0efd103cb9ddcc", "key": "childreNum", "field": "childreNum", "label": "独生子女身份证号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputchildreNum", "name": "input事件", "value": "console.log(form)"}, {"key": "foucschildreNum", "name": "foucs事件", "value": ""}, {"key": "changechildreNum", "name": "change事件", "value": ""}, {"key": "clickchildreNum", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "独生子女身份证号码不能为空", "trigger": "blur", "required": true}, {"message": "独生子女身份证号码格式不正确", "pattern": "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[xX])$", "trigger": "blur"}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "bPW2OloMI", "icon": "icon-timeSelector", "name": "日期选择器", "child": [], "props": {"id": "a06a2f9289184c3fbd2052879206ce26", "key": "childreDate", "type": "date", "field": "childreDate", "label": "独生子女出生日期", "format": "YYYY-MM-DD", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "changechildreDate", "name": "change事件", "value": ""}], "labelWidth": "", "modelValue": null, "tableWidth": "", "isTimeLimit": false, "labelHeight": "", "placeholder": "请选择时间", "valueFormat": "YYYY-MM-DD", "labelDescribe": "", "endPlaceholder": "请选择结束时间", "rangeSeparator": "-", "controlsPosition": "", "startPlaceholder": "请选择开始时间"}, "rules": [{"message": "独生子女出生日期不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": true, "specialName": "DataPickerAtribute", "componentName": "a-net-date-picker"}], "props": {"show": true, "field": "1VusldYRt", "functions": [], "listInterface": "", "saveInterface": "", "isFaceRecognition": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFaceRecognition"}], "props": {"show": true, "field": "nEpgwKduP", "title": "基本信息", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true, "labelDescribe": ""}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}], "formAttribute": {"inline": false, "disabled": false, "tableName": "demoFormName", "labelWidth": "100px", "labelPosition": "right"}, "beforeFunction": "{}", "submitFunction": "{}"}