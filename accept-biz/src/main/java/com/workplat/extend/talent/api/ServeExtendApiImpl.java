package com.workplat.extend.talent.api;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.workplat.accept.business.chat.service.MattersClassifyService;
import com.workplat.extend.talent.entity.HouseholdRegFile;
import com.workplat.extend.talent.service.HouseholdRegFileService;
import com.workplat.extend.talent.vo.HouseRegVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR> cheng
 * @package com.workplat.extend.api
 * @description
 * @date 2025/7/9 16:44
 */
@RestController
public class ServeExtendApiImpl implements ServeExtendApi {

    @Autowired
    private MattersClassifyService mattersClassifyService;
    @Autowired
    private HouseholdRegFileService householdRegFileService;

    @Override
    public ResponseData<Void> saveHouseReg(String fileIds) {
         mattersClassifyService.saveHouseReg(fileIds);
         return ResponseData.success().build();
    }

    @Override
    public ResponseData<Page<HouseRegVO>> queryHouseReg(String registerName, String startTime, String endTime, PageableDTO pageDTO) {
        Map<String, Object> param = Maps.newHashMap();
        if (StringUtils.isNotBlank(registerName)){
            param.put("like(name)", registerName);
        }
        if (StringUtils.isNotBlank(startTime)){
            Date start = DateUtil.parse(startTime);
            param.put(">=(updateTime)", start);
        }
        if (StringUtils.isNotBlank(endTime)){
            Date end = DateUtil.parse(endTime);
            param.put("<=(updateTime)", end);
        }
        pageDTO.setSort(new String[]{"updateTime"});
        Page<HouseRegVO> page = householdRegFileService.queryForPage(param, pageDTO.convertPageable()).map(this::convertHouseReg);
        return ResponseData.success(page);
    }

    @Override
    public ResponseData<Void> deleteById(String id) {
        householdRegFileService.deleteById(id);
        return ResponseData.success().build();
    }

    private HouseRegVO convertHouseReg(HouseholdRegFile entity) {
        HouseRegVO vo = new HouseRegVO();
        vo.setId(entity.getId());
        vo.setRegisterName(entity.getName());
        vo.setRegisterCardId(entity.getCardId());
        vo.setRegisterTime(entity.getUpdateTime());
        vo.setFileName("人才落户登记表_" + entity.getName());
        vo.setFileId(entity.getFileId());
        vo.setDataJson(entity.getDataJson());
        return vo;
    }
}
