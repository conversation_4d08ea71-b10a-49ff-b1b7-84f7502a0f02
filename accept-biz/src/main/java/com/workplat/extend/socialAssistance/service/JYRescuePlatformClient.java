package com.workplat.extend.socialAssistance.service;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.workplat.extend.socialAssistance.dto.RescueUserInfoDto;
import com.workplat.extend.socialAssistance.vo.AppealInfoVo;
import com.workplat.extend.socialAssistance.vo.RescueUserInfoVO;
import com.workplat.gss.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 江阴市智慧大救助管理服务平台接口对接工具类
 */
@Slf4j
@Component
public class JYRescuePlatformClient {

    private static final String BASE_URL = "https://www.jyshjz.cn/jyyjs/api/djz/";
    // 居民端获取认证凭证
    private static final String AUTH_URL = BASE_URL + "Common/Login/thridLogin";
    // 诉求反映保存
    private static final String SAVE_URL = BASE_URL + "c15sqfy/Mdsqfy/sqfySaveV2";
    // 诉求反映详情
    private static final String INFO_URL = BASE_URL + "c15sqfy/Mdsqfy/sqfyInfo";

    private final String appKey = "Aq3TbR6pLm9zKw2v";
    private final String appSecret = "f}*~@m_$&+%s]{[!";
    private final SM4 sm4 = SmUtil.sm4(appSecret.getBytes());

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取认证凭证
     * 调用这个接口的被调用方会再次调用我们项目中下面的authentication接口
     * {@link com.workplat.extend.socialAssistance.api.SocialAssistanceApi#authentication(JSONObject)}
     *
     * @param token 回调第三方接口认证token
     * @return 认证token
     * @throws BusinessException 如果请求失败
     */
    public String getAuthToken(String token) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("appKey", appKey);

        JSONObject dataBody = new JSONObject();
        dataBody.put("body", new JSONObject().fluentPut("token", token));
        requestBody.put("data", sm4.encryptBase64(dataBody.toJSONString()));

        ResponseEntity<String> response = sendRequest(AUTH_URL, requestBody, null);
        return processAuthResponse(response);
    }

    /**
     * 提交诉求申请
     *
     * @param rescueUserInfoDto 救助用户信息
     * @param token             认证token
     * @throws BusinessException 如果请求失败
     */
    public List<RescueUserInfoVO> submitAppeal(RescueUserInfoDto rescueUserInfoDto, String token) {
        log.info("提交诉求申请: {}", JSON.toJSONString(rescueUserInfoDto, JSONWriter.Feature.PrettyFormat));
        JSONObject requestBody = new JSONObject();
        requestBody.put("appKey", appKey);
        requestBody.put("data", sm4.encryptBase64(JSON.toJSONString(rescueUserInfoDto)));

        ResponseEntity<String> response = sendRequest(SAVE_URL, requestBody, token);
        List<RescueUserInfoVO> rescueUserInfoVOList = processResponse(response, "诉求提交成功");
        log.info("提交诉求申请结果: {}", JSON.toJSONString(rescueUserInfoDto, JSONWriter.Feature.PrettyFormat));
        return rescueUserInfoVOList;
    }

    /**
     * 获取诉求申请详情
     * 这个方法没有业务使用到
     *
     * @param id    申请ID
     * @param token 认证token
     * @return 救助用户信息详情
     * @throws BusinessException 如果请求失败
     */
    @Deprecated
    public AppealInfoVo getAppealInfo(String id, String token) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("appKey", appKey);
        requestBody.put("data", sm4.encryptBase64(new JSONObject().fluentPut("id", id).toJSONString()));

        ResponseEntity<String> response = sendRequest(INFO_URL, requestBody, token);
        return processInfoResponse(response);
    }

    /**
     * 发送HTTP请求
     */
    private ResponseEntity<String> sendRequest(String url, JSONObject requestBody, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (token != null) {
            String authToken = getAuthToken(token);
            authToken = authToken.replaceAll("\\{token=|\\}", "");
            headers.set("Authorization", authToken);
        }

        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
        return restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
    }

    /**
     * 处理认证响应
     */
    private String processAuthResponse(ResponseEntity<String> response) {
        JSONObject result = JSON.parseObject(response.getBody());
        if (result.getInteger("code") == 200) {
            return sm4.decryptStr(result.getString("data"));
        }
        throw new BusinessException("认证失败: " + result.getString("msg"));
    }

    /**
     * 处理普通响应
     */
    private List<RescueUserInfoVO> processResponse(ResponseEntity<String> response, String successMessage) {
        JSONObject result = JSON.parseObject(response.getBody());
        if (result.getInteger("code") == 200) {
            String data = sm4.decryptStr(result.getString("data"));
            return JSON.parseArray(data, RescueUserInfoVO.class);
        } else {
            throw new BusinessException("操作失败: " + result.getString("msg"));
        }
    }

    /**
     * 处理信息查询响应
     */
    private AppealInfoVo processInfoResponse(ResponseEntity<String> response) {
        JSONObject result = JSON.parseObject(response.getBody());
        if (result.getInteger("code") == 200) {
            String data = sm4.decryptStr(result.getString("data"));
            return JSON.parseObject(data, AppealInfoVo.class);
        }
        throw new BusinessException("查询失败: " + result.getString("msg"));
    }
}
