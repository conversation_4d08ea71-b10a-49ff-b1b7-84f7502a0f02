package com.workplat.extend.socialAssistance.api.impl;

import cn.hutool.http.Header;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.extend.socialAssistance.api.SocialAssistanceApi;
import com.workplat.extend.socialAssistance.vo.ReturnModel;
import com.workplat.extend.socialAssistance.vo.UserInfoVo;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@CrossOrigin
@RestController
public class SocialAssistanceApiImpl implements SocialAssistanceApi {

    @ApiLogging(operation = "社会救助认证", module = "社会救助", type = OperationType.QUERY)
    @Override
    public ReturnModel authentication(@RequestBody JSONObject tokenBody) {
        String token = tokenBody.getString("token");
        token = token.startsWith("Bearer ") ? token : "Bearer " + token;

        // token换用户
        String url = "/allinone-api/api/ucenter/user/get";
        String responseString =
                GatewayUtils.executeGetRequest(url,
                        ImmutableMap.of(Header.AUTHORIZATION.getValue(), token),
                        null, false, false);
        if (responseString.contains("error")) {
            return ReturnModel.builder().isSuccess(false).message("token不存在").build();
        }
        JSONObject userJson = JSONObject.parseObject(responseString);
        UserInfoVo userInfoVo = new UserInfoVo();
        userInfoVo.setName(userJson.getString("realName"));
        userInfoVo.setIdcard(userJson.getString("certificateNumber"));
        userInfoVo.setMobile(userJson.getString("mobile"));

        return ReturnModel.builder().isSuccess(true).data(userInfoVo).build();

    }


}
