package com.workplat.push.ga.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;


@Configuration
public class GaJdbcTemplateConfig {

    public static final String DRIVER_CLASS_NAME = "com.mysql.cj.jdbc.Driver";
    public static final String URL = "**************************************************************************************************************************************************************************************************************************************";
    public static final String USER_NAME = "root";
    public static final String PASSWORD = "Risesoft123!@";

    @Value("${ga.datasource.driver-class-name:}")
    private String driverClassName;

    @Value("${ga.datasource.url}")
    private String url;

    @Value("${ga.datasource.username}")
    private String username;

    @Value("${ga.datasource.password}")
    private String password;

    private DruidDataSource gaDataSourceInstance;

    @Bean(name = "gaJdbcTemplate")
    public NamedParameterJdbcTemplate gaJdbcTemplate() {
        //创建JdbcTemplate对象，设置数据源
        return new NamedParameterJdbcTemplate(getGaDataSourceInstance());
    }

    @Bean(name = "gaTransactionTemplate")
    public TransactionTemplate gaTransactionTemplate() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager(getGaDataSourceInstance());
        return new TransactionTemplate(transactionManager);
    }

    /**
     * 获取GA数据源单例，确保JdbcTemplate和TransactionManager使用同一个数据源
     */
    private synchronized DruidDataSource getGaDataSourceInstance() {
        if (gaDataSourceInstance == null) {
            gaDataSourceInstance = createGaDataSource();
        }
        return gaDataSourceInstance;
    }

    /**
     * 创建GA数据源，不注册为Spring Bean，避免被JPA识别
     */
    private DruidDataSource createGaDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driverClassName);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setInitialSize(5);
        dataSource.setMinIdle(5);
        dataSource.setMaxActive(20);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        dataSource.setPoolPreparedStatements(false);
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(20);
        return dataSource;
    }
}
