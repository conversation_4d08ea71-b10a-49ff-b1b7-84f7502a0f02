package com.workplat.push.entity;

import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

/**
 * @ClassName: InstancePushLog
 * @Description:办件推送日志
 * @Author: Yang Fan
 * @Date: 2025-07-10 15:13
 * @Version
 **/
@Setter
@Getter
@Entity
@Table(name = "instance_push_log")
public class InstancePushLog extends BaseEntity {

    /**
     * 办件id
     */
    @OneToOne
    @JoinColumn(name = "instance_id")
    @Comment("申报对象")
    @Where(clause = "deleted = 0")
    private BizInstanceInfo instance;

    /**
     * 推送状态 0:未推送 1:推送成功 2:推送失败
     */
    @Column(name = "push_status", length = 32)
    private String pushStatus;

    /**
     * 推送时间
     */
    @Column(name = "push_time")
    private java.util.Date pushTime;

    /**
     * 推送结果
     */
    @Column(name = "push_result", columnDefinition = "LONGTEXT")
    private String pushResult;
}
