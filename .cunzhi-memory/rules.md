# 开发规范和规则

- 全局事务问题修复：1. 修复GaDeclarePushService中SQL语法错误(now1()改为now())；2. 为GA数据源配置独立的事务管理器gaTransactionManager；3. 在GaDeclarePushService.push()方法中指定使用gaTransactionManager确保事务正确回滚
- Spring定时任务重复执行问题：使用@Scheduled注解时必须在启动类添加@EnableScheduling注解，否则可能导致定时任务重复执行。缺少@EnableScheduling会导致调度器初始化不完整，造成执行上下文不稳定和事务边界不清晰，最终引起同一任务被重复调用。
- 组件引擎优先级策略：使用@Order注解控制组件选择优先级，特定业务组件(1-20)优先于默认实现组件(80-99)优先于抽象基类(100+)。已修复充电桩情形选择、申报须知、材料分类组件的选择冲突问题。
