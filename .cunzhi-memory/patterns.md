- Gateway客户端重构：将GatewayUtils静态方法改造为Service Bean模式，支持多配置实例管理。创建了GatewayService和GatewayConfig，通过@Qualifier注入不同业务场景的Gateway配置（如defaultGatewayService、powerGatewayService），提供更好的可测试性和配置管理。保持向后兼容，GatewayUtils作为兼容层委托给Bean处理。
- MaterialsClassifyComponentEngine表单过滤模式冲突解决方案：通过ComponentFlowService动态获取下一个组件类型，根据engineCode决定过滤模式。FieldShow组件使用保留模式(显示AI已提取字段)，FormGroup组件使用移除模式(显示需填写字段)，其他组件默认移除模式。
- 创建ComponentEngineCode常量类统一管理所有组件引擎的engineCode，避免硬编码字符串。包含shouldUseKeepMode()方法判断表单过滤模式。已更新MaterialsClassifyComponentEngine、FieldShowComponentEngine、FormGroupComponentEngine、AbstractMaterialClassifyEngine使用新常量。
- 组件属性获取通用化方案：创建了ComponentPropsManager统一管理组件属性获取和合并，支持extendVO属性覆盖content默认配置的优先级处理。增强了AbstractComponentEngine提供便捷的属性获取方法如getTips()、getPropertyValue()等，并保持向后兼容。更新了ConfigBindingUtil支持直接从ComponentDataContext绑定配置。提供了ComponentPropsValidator进行配置验证和ComponentPropsMigrationGuide迁移指南。
