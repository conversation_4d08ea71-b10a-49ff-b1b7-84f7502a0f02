{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "边聊边办流程配置", "description": "", "directory": "/.fastRequest/collections/Root/accept-api/ConfFlowApi", "domain": "http://localhost:8299", "enableEnv": "local", "enableProject": "accept-api", "filePath": "/.fastRequest/collections/Root/accept-api/ConfFlowApi~getComponentProperties.rapi", "id": "api_com.workplat.flow.ConfFlowApi.getComponentProperties", "mark": false, "module": "accept-api", "name": "获取多流程组件编辑属性", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "", "bodyParamMap": {}, "classDescription": "边聊边办流程配置", "className": "com.workplat.flow.ConfFlowApi", "headerParamsKeyValueList": [], "jsonDocument": "", "method": "getComponentProperties", "methodDescription": "获取多流程组件编辑属性", "methodType": "GET", "multipartKeyValueListJson": "[]", "originUrl": "/api/flow/flowable/modelInfo/getComponentProperties", "pathParamsKeyValueListJson": "[]", "postScript": "", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应信息\",\"errorMessage\":\"错误信息\",\"data\":{}}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": "200"}, "message": {"comment": "响应信息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "请求成功"}, "errorMessage": {"comment": "错误信息", "customFlag": 2, "enabled": true, "key": "errorMessage", "type": "String", "value": "报错信息"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {}}}}}, "tempId": "", "url": "/api/flow/flowable/modelInfo/getComponentProperties", "urlEncodedKeyValueListJson": "[]", "urlEncodedKeyValueListText": "", "urlParamsKeyValueListJson": "[{\"comment\":\"\",\"customFlag\":2,\"enabled\":true,\"key\":\"codes\",\"type\":\"String\",\"value\":\"codes_bbrks\"}]", "urlParamsKeyValueListText": "codes=codes_bbrks"}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.workplat.flow.ConfFlowApi.getComponentProperties", "type": 2}